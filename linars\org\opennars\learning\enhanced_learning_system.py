"""
增强版推理学习系统
实现更完善的推理学习机制，包括经验学习、规则学习、模式识别和知识泛化

主要特性：
1. 经验驱动的学习机制
2. 规则自动发现和优化
3. 模式识别和泛化
4. 知识结构化组织
5. 学习效果评估和反馈
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import threading
import numpy as np
from abc import ABC, abstractmethod

from ..entity.task import Task
from ..entity.sentence import Sentence
from ..entity.truth_value import TruthValue
from ..entity.budget_value import BudgetValue
from ..inference.enhanced_reasoning_engine import ReasoningResult, ReasoningPath


class LearningType(Enum):
    """学习类型"""
    EXPERIENCE = "experience"      # 经验学习
    RULE = "rule"                 # 规则学习
    PATTERN = "pattern"           # 模式学习
    GENERALIZATION = "generalization"  # 泛化学习
    SPECIALIZATION = "specialization"  # 特化学习


class LearningStrategy(Enum):
    """学习策略"""
    INCREMENTAL = "incremental"    # 增量学习
    BATCH = "batch"               # 批量学习
    REINFORCEMENT = "reinforcement"  # 强化学习
    UNSUPERVISED = "unsupervised"  # 无监督学习


@dataclass
class LearningExperience:
    """学习经验记录"""
    experience_id: str
    learning_type: LearningType
    input_data: List[Any]
    output_data: List[Any]
    success: bool
    confidence: float
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LearnedRule:
    """学习到的规则"""
    rule_id: str
    rule_type: str
    conditions: List[Any]
    conclusions: List[Any]
    confidence: float
    usage_count: int = 0
    success_rate: float = 0.0
    created_time: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)


@dataclass
class PatternTemplate:
    """模式模板"""
    pattern_id: str
    pattern_type: str
    structure: Dict[str, Any]
    instances: List[Any] = field(default_factory=list)
    confidence: float = 0.0
    generality: float = 0.0  # 泛化程度
    specificity: float = 0.0  # 特化程度


class LearningModule(ABC):
    """学习模块抽象基类"""
    
    @abstractmethod
    async def learn(self, experiences: List[LearningExperience]) -> Dict[str, Any]:
        """学习方法"""
        pass
    
    @abstractmethod
    def get_learned_knowledge(self) -> Dict[str, Any]:
        """获取学习到的知识"""
        pass


class ExperienceLearningModule(LearningModule):
    """经验学习模块"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.experiences: List[LearningExperience] = []
        self.experience_patterns: Dict[str, List[LearningExperience]] = defaultdict(list)
        self.success_patterns: Dict[str, float] = defaultdict(float)
        
    async def learn(self, experiences: List[LearningExperience]) -> Dict[str, Any]:
        """从经验中学习"""
        learned_patterns = {}
        
        for exp in experiences:
            self.experiences.append(exp)
            
            # 按类型分组经验
            pattern_key = f"{exp.learning_type.value}_{len(exp.input_data)}"
            self.experience_patterns[pattern_key].append(exp)
            
            # 更新成功模式
            if exp.success:
                self.success_patterns[pattern_key] = (
                    self.success_patterns[pattern_key] * 0.9 + exp.confidence * 0.1
                )
            
            # 识别成功经验的共同特征
            if exp.success and exp.confidence > 0.7:
                pattern = self._extract_success_pattern(exp)
                if pattern:
                    learned_patterns[exp.experience_id] = pattern
        
        return {
            'learned_patterns': learned_patterns,
            'total_experiences': len(self.experiences),
            'pattern_types': len(self.experience_patterns)
        }
    
    def _extract_success_pattern(self, experience: LearningExperience) -> Optional[Dict]:
        """提取成功经验的模式"""
        try:
            # 分析输入输出的结构特征
            input_features = self._analyze_structure(experience.input_data)
            output_features = self._analyze_structure(experience.output_data)
            
            return {
                'input_features': input_features,
                'output_features': output_features,
                'confidence': experience.confidence,
                'learning_type': experience.learning_type.value
            }
        except Exception as e:
            self.logger.warning(f"Failed to extract pattern: {e}")
            return None
    
    def _analyze_structure(self, data: List[Any]) -> Dict[str, Any]:
        """分析数据结构特征"""
        features = {
            'count': len(data),
            'types': list(set(type(item).__name__ for item in data)),
            'complexity': self._calculate_complexity(data)
        }
        return features
    
    def _calculate_complexity(self, data: List[Any]) -> float:
        """计算数据复杂度"""
        if not data:
            return 0.0
        
        # 简单的复杂度计算：基于数据类型多样性和嵌套深度
        type_diversity = len(set(type(item).__name__ for item in data)) / len(data)
        avg_depth = sum(self._get_depth(item) for item in data) / len(data)
        
        return type_diversity * 0.5 + min(avg_depth / 10, 0.5)
    
    def _get_depth(self, obj, depth=0) -> int:
        """获取对象嵌套深度"""
        if isinstance(obj, (list, tuple)):
            return max(self._get_depth(item, depth + 1) for item in obj) if obj else depth
        elif isinstance(obj, dict):
            return max(self._get_depth(value, depth + 1) for value in obj.values()) if obj else depth
        else:
            return depth
    
    def get_learned_knowledge(self) -> Dict[str, Any]:
        """获取学习到的知识"""
        return {
            'experience_count': len(self.experiences),
            'pattern_types': dict(self.experience_patterns),
            'success_patterns': dict(self.success_patterns),
            'recent_experiences': self.experiences[-10:] if self.experiences else []
        }


class RuleLearningModule(LearningModule):
    """规则学习模块"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.learned_rules: Dict[str, LearnedRule] = {}
        self.rule_templates: Dict[str, Dict] = {}
        self.rule_performance: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        
    async def learn(self, experiences: List[LearningExperience]) -> Dict[str, Any]:
        """从经验中学习规则"""
        new_rules = {}
        
        # 按学习类型分组经验
        grouped_experiences = defaultdict(list)
        for exp in experiences:
            grouped_experiences[exp.learning_type].append(exp)
        
        # 为每种类型学习规则
        for learning_type, type_experiences in grouped_experiences.items():
            if len(type_experiences) >= 3:  # 至少需要3个经验来学习规则
                rules = await self._discover_rules(learning_type, type_experiences)
                new_rules.update(rules)
        
        # 更新规则库
        for rule_id, rule in new_rules.items():
            self.learned_rules[rule_id] = rule
        
        return {
            'new_rules': len(new_rules),
            'total_rules': len(self.learned_rules),
            'rule_types': list(set(rule.rule_type for rule in self.learned_rules.values()))
        }
    
    async def _discover_rules(self, learning_type: LearningType, 
                            experiences: List[LearningExperience]) -> Dict[str, LearnedRule]:
        """发现规则"""
        discovered_rules = {}
        
        # 分析成功经验的共同模式
        successful_experiences = [exp for exp in experiences if exp.success and exp.confidence > 0.5]
        
        if len(successful_experiences) >= 2:
            # 寻找输入-输出模式
            patterns = self._find_io_patterns(successful_experiences)
            
            for pattern_id, pattern_data in patterns.items():
                rule = LearnedRule(
                    rule_id=f"rule_{learning_type.value}_{uuid.uuid4().hex[:8]}",
                    rule_type=f"{learning_type.value}_pattern",
                    conditions=pattern_data['conditions'],
                    conclusions=pattern_data['conclusions'],
                    confidence=pattern_data['confidence']
                )
                discovered_rules[rule.rule_id] = rule
        
        return discovered_rules
    
    def _find_io_patterns(self, experiences: List[LearningExperience]) -> Dict[str, Dict]:
        """寻找输入输出模式"""
        patterns = {}
        
        # 简化的模式发现：寻找相似的输入输出结构
        for i, exp1 in enumerate(experiences):
            for j, exp2 in enumerate(experiences[i+1:], i+1):
                similarity = self._calculate_similarity(exp1, exp2)
                
                if similarity > 0.7:  # 高相似度
                    pattern_id = f"pattern_{i}_{j}"
                    patterns[pattern_id] = {
                        'conditions': self._generalize_conditions([exp1.input_data, exp2.input_data]),
                        'conclusions': self._generalize_conclusions([exp1.output_data, exp2.output_data]),
                        'confidence': (exp1.confidence + exp2.confidence) / 2 * similarity
                    }
        
        return patterns
    
    def _calculate_similarity(self, exp1: LearningExperience, exp2: LearningExperience) -> float:
        """计算经验相似度"""
        # 简化的相似度计算
        input_sim = self._structure_similarity(exp1.input_data, exp2.input_data)
        output_sim = self._structure_similarity(exp1.output_data, exp2.output_data)
        
        return (input_sim + output_sim) / 2
    
    def _structure_similarity(self, data1: List[Any], data2: List[Any]) -> float:
        """计算结构相似度"""
        if len(data1) != len(data2):
            return 0.0
        
        if not data1:
            return 1.0
        
        type_matches = sum(1 for a, b in zip(data1, data2) if type(a) == type(b))
        return type_matches / len(data1)
    
    def _generalize_conditions(self, condition_sets: List[List[Any]]) -> List[Any]:
        """泛化条件"""
        # 简化实现：返回第一个条件集
        return condition_sets[0] if condition_sets else []
    
    def _generalize_conclusions(self, conclusion_sets: List[List[Any]]) -> List[Any]:
        """泛化结论"""
        # 简化实现：返回第一个结论集
        return conclusion_sets[0] if conclusion_sets else []
    
    def get_learned_knowledge(self) -> Dict[str, Any]:
        """获取学习到的知识"""
        return {
            'rule_count': len(self.learned_rules),
            'rules': {rule_id: {
                'rule_type': rule.rule_type,
                'confidence': rule.confidence,
                'usage_count': rule.usage_count,
                'success_rate': rule.success_rate
            } for rule_id, rule in self.learned_rules.items()},
            'rule_performance': dict(self.rule_performance)
        }


class EnhancedLearningSystem:
    """增强版学习系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 学习模块
        self.experience_module = ExperienceLearningModule()
        self.rule_module = RuleLearningModule()
        
        # 学习历史和统计
        self.learning_history: List[Dict] = []
        self.learning_stats = defaultdict(int)
        
        # 配置参数
        self.config = {
            'batch_size': 50,
            'learning_interval': 60.0,  # 秒
            'min_experiences_for_learning': 10,
            'confidence_threshold': 0.3
        }
        
        # 启动学习循环
        self._start_learning_loop()
    
    def _start_learning_loop(self):
        """启动学习循环"""
        def learning_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self._continuous_learning())
        
        learning_thread = threading.Thread(target=learning_loop, daemon=True)
        learning_thread.start()
    
    async def _continuous_learning(self):
        """持续学习循环"""
        while True:
            try:
                await asyncio.sleep(self.config['learning_interval'])
                await self._perform_learning_cycle()
            except Exception as e:
                self.logger.error(f"Error in learning loop: {e}")
    
    async def _perform_learning_cycle(self):
        """执行学习周期"""
        # 这里应该从推理引擎获取最新的经验数据
        # 暂时跳过具体实现
        pass
    
    async def learn_from_reasoning_results(self, results: List[ReasoningResult]) -> Dict[str, Any]:
        """从推理结果中学习"""
        experiences = []
        
        for result in results:
            # 将推理结果转换为学习经验
            experience = LearningExperience(
                experience_id=f"reasoning_{result.request_id}",
                learning_type=LearningType.EXPERIENCE,
                input_data=result.metadata.get('premises', []),
                output_data=result.conclusions,
                success=result.success,
                confidence=result.confidence,
                metadata={
                    'strategy_used': result.strategy_used.value,
                    'execution_time': result.execution_time,
                    'reasoning_paths': len(result.reasoning_paths)
                }
            )
            experiences.append(experience)
        
        # 使用各个学习模块进行学习
        experience_results = await self.experience_module.learn(experiences)
        rule_results = await self.rule_module.learn(experiences)
        
        # 记录学习历史
        learning_record = {
            'timestamp': time.time(),
            'experience_count': len(experiences),
            'experience_results': experience_results,
            'rule_results': rule_results
        }
        self.learning_history.append(learning_record)
        
        # 更新统计
        self.learning_stats['total_learning_cycles'] += 1
        self.learning_stats['total_experiences_processed'] += len(experiences)
        
        return learning_record
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        return {
            'learning_stats': dict(self.learning_stats),
            'experience_knowledge': self.experience_module.get_learned_knowledge(),
            'rule_knowledge': self.rule_module.get_learned_knowledge(),
            'recent_learning_history': self.learning_history[-10:] if self.learning_history else []
        }


# 全局学习系统实例
enhanced_learning_system = None

def get_enhanced_learning_system() -> EnhancedLearningSystem:
    """获取增强学习系统实例"""
    global enhanced_learning_system
    if enhanced_learning_system is None:
        enhanced_learning_system = EnhancedLearningSystem()
    return enhanced_learning_system
