"""
增强版推理系统演示脚本
展示优化后的NARS推理系统、学习机制、NARS-LIDA集成等功能

主要演示内容：
1. 增强版NARS推理引擎
2. 推理学习机制
3. NARS-LIDA深度集成
4. 图式搜索与推理融合
5. 变量绑定系统
"""

import asyncio
import logging
import time
import sys
import os
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 导入增强模块
try:
    from org.opennars.inference.enhanced_reasoning_engine import (
        get_enhanced_reasoning_engine, ReasoningRequest, ReasoningStrategy, ReasoningPriority
    )
    from org.opennars.learning.enhanced_learning_system import get_enhanced_learning_system
    from edu.memphis.ccrg.lida.Integration.enhanced_nars_lida_integration import (
        get_enhanced_integration_system, CognitiveEvent, IntegrationMode
    )
    from edu.memphis.ccrg.lida.Nlanguage.enhanced_schema_reasoning_fusion import (
        get_schema_reasoning_fusion_engine, SearchReasoningMode, FusionStrategy
    )
    from org.opennars.language.enhanced_variable_binding import (
        get_enhanced_variable_binding_system, VariableType, BindingScope
    )
    MODULES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Some enhanced modules not available: {e}")
    MODULES_AVAILABLE = False


class EnhancedReasoningDemo:
    """增强推理系统演示类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if not MODULES_AVAILABLE:
            self.logger.error("Enhanced modules not available, demo cannot run")
            return
        
        # 初始化各个系统
        self.reasoning_engine = get_enhanced_reasoning_engine()
        self.learning_system = get_enhanced_learning_system()
        self.integration_system = get_enhanced_integration_system()
        self.fusion_engine = get_schema_reasoning_fusion_engine()
        self.binding_system = get_enhanced_variable_binding_system()
        
        self.logger.info("Enhanced reasoning demo initialized")
    
    async def demo_enhanced_reasoning(self):
        """演示增强推理引擎"""
        self.logger.info("=== 演示增强推理引擎 ===")
        
        try:
            # 创建推理请求
            request = ReasoningRequest(
                request_id="demo_reasoning_001",
                premises=["鸟会飞", "企鹅是鸟", "企鹅不会飞"],  # 简化的前提
                strategy=ReasoningStrategy.ADAPTIVE,
                priority=ReasoningPriority.HIGH,
                max_steps=5,
                timeout=10.0
            )
            
            # 提交推理请求
            request_id = await self.reasoning_engine.submit_reasoning_request(request)
            self.logger.info(f"提交推理请求: {request_id}")
            
            # 获取推理结果
            result = await self.reasoning_engine.get_reasoning_result(request_id, timeout=15.0)
            
            if result:
                self.logger.info(f"推理成功: {result.success}")
                self.logger.info(f"推理置信度: {result.confidence:.3f}")
                self.logger.info(f"执行时间: {result.execution_time:.3f}秒")
                self.logger.info(f"使用策略: {result.strategy_used.value}")
                self.logger.info(f"结论数量: {len(result.conclusions)}")
                self.logger.info(f"推理路径数量: {len(result.reasoning_paths)}")
            else:
                self.logger.warning("推理结果获取超时")
            
            # 获取推理统计
            stats = self.reasoning_engine.get_statistics()
            self.logger.info(f"推理引擎统计: {stats}")
            
        except Exception as e:
            self.logger.error(f"推理演示出错: {e}")
    
    async def demo_learning_system(self):
        """演示学习系统"""
        self.logger.info("=== 演示学习系统 ===")
        
        try:
            # 模拟一些推理结果用于学习
            from org.opennars.learning.enhanced_learning_system import LearningExperience, LearningType
            
            experiences = []
            for i in range(5):
                experience = LearningExperience(
                    experience_id=f"exp_{i}",
                    learning_type=LearningType.EXPERIENCE,
                    input_data=[f"前提{i}_1", f"前提{i}_2"],
                    output_data=[f"结论{i}"],
                    success=i % 2 == 0,  # 模拟成功失败
                    confidence=0.7 + i * 0.05
                )
                experiences.append(experience)
            
            # 执行学习
            learning_result = await self.learning_system.experience_module.learn(experiences)
            self.logger.info(f"经验学习结果: {learning_result}")
            
            rule_learning_result = await self.learning_system.rule_module.learn(experiences)
            self.logger.info(f"规则学习结果: {rule_learning_result}")
            
            # 获取学习统计
            stats = self.learning_system.get_learning_statistics()
            self.logger.info(f"学习系统统计: {stats}")
            
        except Exception as e:
            self.logger.error(f"学习演示出错: {e}")
    
    async def demo_integration_system(self):
        """演示NARS-LIDA集成系统"""
        self.logger.info("=== 演示NARS-LIDA集成系统 ===")
        
        try:
            # 创建集成上下文
            from edu.memphis.ccrg.lida.Integration.enhanced_nars_lida_integration import (
                IntegrationMode, ActivationFlow
            )
            
            context_id = await self.integration_system.create_integration_context(
                mode=IntegrationMode.ADAPTIVE,
                activation_flow=ActivationFlow.BIDIRECTIONAL
            )
            self.logger.info(f"创建集成上下文: {context_id}")
            
            # 提交认知事件
            event = CognitiveEvent(
                event_id="demo_event_001",
                event_type="reasoning_result",
                source_module="NARS",
                target_module="LIDA",
                data="推理结果数据",
                activation=0.8,
                confidence=0.7
            )
            
            await self.integration_system.submit_cognitive_event(event)
            self.logger.info("提交认知事件")
            
            # 等待处理
            await asyncio.sleep(2.0)
            
            # 获取集成统计
            stats = self.integration_system.get_integration_statistics()
            self.logger.info(f"集成系统统计: {stats}")
            
        except Exception as e:
            self.logger.error(f"集成演示出错: {e}")
    
    async def demo_fusion_engine(self):
        """演示图式推理融合引擎"""
        self.logger.info("=== 演示图式推理融合引擎 ===")
        
        try:
            # 创建融合上下文
            context_id = await self.fusion_engine.create_fusion_context(
                search_query="动物 飞行 能力",
                reasoning_premises=["鸟类", "飞行", "翅膀"],
                mode=SearchReasoningMode.INTEGRATED
            )
            self.logger.info(f"创建融合上下文: {context_id}")
            
            # 执行融合
            result = await self.fusion_engine.execute_fusion(
                context_id, 
                strategy=FusionStrategy.DYNAMIC
            )
            
            if result:
                self.logger.info(f"融合成功: {result.success}")
                self.logger.info(f"融合置信度: {result.confidence:.3f}")
                self.logger.info(f"执行时间: {result.execution_time:.3f}秒")
                self.logger.info(f"使用策略: {result.fusion_strategy_used.value}")
                self.logger.info(f"搜索结果数量: {len(result.search_results)}")
                self.logger.info(f"推理结论数量: {len(result.reasoning_conclusions)}")
                self.logger.info(f"集成知识数量: {len(result.integrated_knowledge)}")
            else:
                self.logger.warning("融合结果为空")
            
            # 获取融合统计
            stats = self.fusion_engine.get_fusion_statistics()
            self.logger.info(f"融合引擎统计: {stats}")
            
        except Exception as e:
            self.logger.error(f"融合演示出错: {e}")
    
    async def demo_variable_binding(self):
        """演示变量绑定系统"""
        self.logger.info("=== 演示变量绑定系统 ===")
        
        try:
            # 创建新的绑定上下文
            context_id = self.binding_system.create_context("demo_context")
            self.binding_system.switch_context(context_id)
            self.logger.info(f"创建并切换到上下文: {context_id}")
            
            # 绑定变量
            success = self.binding_system.bind_variable(
                variable_name="X",
                value="鸟类",
                variable_type=VariableType.INDEPENDENT,
                scope=BindingScope.CONTEXT,
                confidence=0.9
            )
            self.logger.info(f"绑定变量X: {success}")
            
            success = self.binding_system.bind_variable(
                variable_name="Y",
                value="飞行能力",
                variable_type=VariableType.DEPENDENT,
                scope=BindingScope.CONTEXT,
                confidence=0.8
            )
            self.logger.info(f"绑定变量Y: {success}")
            
            # 获取绑定
            binding_x = self.binding_system.get_binding("X")
            if binding_x:
                self.logger.info(f"变量X绑定: {binding_x.value}, 置信度: {binding_x.confidence}")
            
            binding_y = self.binding_system.get_binding("Y")
            if binding_y:
                self.logger.info(f"变量Y绑定: {binding_y.value}, 置信度: {binding_y.confidence}")
            
            # 测试上下文切换
            self.binding_system.pop_context()
            self.logger.info("弹出上下文")
            
            # 检查绑定是否还存在
            binding_x_after = self.binding_system.get_binding("X")
            self.logger.info(f"上下文切换后变量X绑定: {binding_x_after is not None}")
            
            # 获取绑定统计
            stats = self.binding_system.get_statistics()
            self.logger.info(f"绑定系统统计: {stats}")
            
        except Exception as e:
            self.logger.error(f"变量绑定演示出错: {e}")
    
    async def demo_integrated_scenario(self):
        """演示集成场景"""
        self.logger.info("=== 演示集成场景 ===")
        
        try:
            # 场景：动物推理问题
            self.logger.info("场景：动物推理问题")
            
            # 1. 设置变量绑定
            self.binding_system.bind_variable("动物", "鸟类", VariableType.INDEPENDENT)
            self.binding_system.bind_variable("属性", "飞行", VariableType.DEPENDENT)
            
            # 2. 创建推理请求
            request = ReasoningRequest(
                request_id="integrated_demo",
                premises=["所有鸟类都有翅膀", "有翅膀的动物可能会飞", "企鹅是鸟类但不会飞"],
                strategy=ReasoningStrategy.ADAPTIVE,
                timeout=10.0
            )
            
            # 3. 提交推理并获取结果
            request_id = await self.reasoning_engine.submit_reasoning_request(request)
            reasoning_result = await self.reasoning_engine.get_reasoning_result(request_id)
            
            if reasoning_result:
                self.logger.info(f"推理结果置信度: {reasoning_result.confidence:.3f}")
                
                # 4. 将推理结果用于学习
                learning_results = await self.learning_system.learn_from_reasoning_results([reasoning_result])
                self.logger.info(f"学习结果: {learning_results}")
                
                # 5. 创建认知事件进行集成
                event = CognitiveEvent(
                    event_id="integrated_event",
                    event_type="reasoning_learning",
                    source_module="NARS",
                    target_module="LIDA",
                    data=reasoning_result,
                    activation=reasoning_result.confidence,
                    confidence=reasoning_result.confidence
                )
                
                await self.integration_system.submit_cognitive_event(event)
                
                # 6. 等待处理并获取最终统计
                await asyncio.sleep(3.0)
                
                integration_stats = self.integration_system.get_integration_statistics()
                self.logger.info(f"最终集成统计: {integration_stats}")
            
        except Exception as e:
            self.logger.error(f"集成场景演示出错: {e}")
    
    async def run_all_demos(self):
        """运行所有演示"""
        if not MODULES_AVAILABLE:
            self.logger.error("增强模块不可用，无法运行演示")
            return
        
        self.logger.info("开始运行增强推理系统演示")
        
        demos = [
            ("增强推理引擎", self.demo_enhanced_reasoning),
            ("学习系统", self.demo_learning_system),
            ("集成系统", self.demo_integration_system),
            ("融合引擎", self.demo_fusion_engine),
            ("变量绑定", self.demo_variable_binding),
            ("集成场景", self.demo_integrated_scenario)
        ]
        
        for demo_name, demo_func in demos:
            try:
                self.logger.info(f"\n开始演示: {demo_name}")
                await demo_func()
                self.logger.info(f"完成演示: {demo_name}")
                await asyncio.sleep(1.0)  # 短暂暂停
            except Exception as e:
                self.logger.error(f"演示 {demo_name} 失败: {e}")
        
        self.logger.info("\n所有演示完成")
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'integration_system'):
                self.integration_system.shutdown()
            if hasattr(self, 'fusion_engine'):
                self.fusion_engine.shutdown()
            self.logger.info("资源清理完成")
        except Exception as e:
            self.logger.error(f"资源清理出错: {e}")


async def main():
    """主函数"""
    demo = EnhancedReasoningDemo()
    
    try:
        await demo.run_all_demos()
    except KeyboardInterrupt:
        logger.info("演示被用户中断")
    except Exception as e:
        logger.error(f"演示运行出错: {e}")
    finally:
        demo.cleanup()


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        sys.exit(1)
