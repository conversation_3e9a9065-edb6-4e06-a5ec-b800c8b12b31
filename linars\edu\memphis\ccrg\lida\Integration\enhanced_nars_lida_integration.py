"""
增强版NARS-LIDA深度集成系统
实现NARS推理系统与LIDA认知架构之间的深度集成和优化

主要特性：
1. 推理-感知-行动循环的深度集成
2. 动态激活传播和推理触发
3. 上下文感知的转换机制
4. 双向反馈和学习机制
5. 性能监控和自适应优化
6. 异步处理和并发控制
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import threading
import weakref
import numpy as np

# NARS相关导入
try:
    from linars.org.opennars.inference.enhanced_reasoning_engine import (
        get_enhanced_reasoning_engine, ReasoningRequest, ReasoningStrategy, 
        ReasoningPriority, ReasoningResult
    )
    from linars.org.opennars.learning.enhanced_learning_system import get_enhanced_learning_system
    from linars.org.opennars.entity.task import Task
    from linars.org.opennars.entity.sentence import Sentence
    from linars.org.opennars.entity.truth_value import TruthValue
    from linars.edu.memphis.ccrg.linars.term import Term
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

# LIDA相关导入
try:
    from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
    from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
    from linars.edu.memphis.ccrg.lida.PAM.PamNode import PamNode
    from linars.edu.memphis.ccrg.lida.PAM.PamLink import PamLink
    from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent
    from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
    LIDA_AVAILABLE = True
except ImportError:
    LIDA_AVAILABLE = False


class IntegrationMode(Enum):
    """集成模式"""
    PASSIVE = "passive"          # 被动集成，仅响应请求
    ACTIVE = "active"           # 主动集成，主动触发推理
    ADAPTIVE = "adaptive"       # 自适应集成，根据情况调整
    CONTINUOUS = "continuous"   # 持续集成，实时同步


class ActivationFlow(Enum):
    """激活流向"""
    NARS_TO_LIDA = "nars_to_lida"
    LIDA_TO_NARS = "lida_to_nars"
    BIDIRECTIONAL = "bidirectional"
    CIRCULAR = "circular"


@dataclass
class IntegrationContext:
    """集成上下文"""
    context_id: str
    mode: IntegrationMode
    activation_flow: ActivationFlow
    nars_elements: List[Any] = field(default_factory=list)
    lida_elements: List[Any] = field(default_factory=list)
    activation_levels: Dict[str, float] = field(default_factory=dict)
    reasoning_history: List[Dict] = field(default_factory=list)
    created_time: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    
    def update_activation(self, element_id: str, activation: float):
        """更新激活水平"""
        self.activation_levels[element_id] = activation
        self.last_updated = time.time()
    
    def get_average_activation(self) -> float:
        """获取平均激活水平"""
        if not self.activation_levels:
            return 0.0
        return sum(self.activation_levels.values()) / len(self.activation_levels)


@dataclass
class CognitiveEvent:
    """认知事件"""
    event_id: str
    event_type: str
    source_module: str
    target_module: str
    data: Any
    activation: float
    confidence: float
    timestamp: float = field(default_factory=time.time)
    processed: bool = False
    
    def mark_processed(self):
        """标记为已处理"""
        self.processed = True


class EnhancedNarsLidaIntegration:
    """增强版NARS-LIDA深度集成系统"""
    
    def __init__(self, nar_instance=None, lida_agent=None):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 核心组件
        self.nar = nar_instance
        self.lida_agent = lida_agent
        self.reasoning_engine = get_enhanced_reasoning_engine(nar_instance) if NARS_AVAILABLE else None
        self.learning_system = get_enhanced_learning_system() if NARS_AVAILABLE else None
        
        # 集成状态管理
        self.integration_contexts: Dict[str, IntegrationContext] = {}
        self.cognitive_events: deque = deque(maxlen=1000)
        self.active_coalitions: Dict[str, Any] = {}
        
        # 激活传播网络
        self.activation_network: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        self.activation_thresholds = {
            'reasoning_trigger': 0.7,
            'attention_focus': 0.8,
            'action_selection': 0.9
        }
        
        # 配置参数
        self.config = {
            'integration_mode': IntegrationMode.ADAPTIVE,
            'max_concurrent_contexts': 20,
            'activation_decay_rate': 0.95,
            'reasoning_timeout': 15.0,
            'event_processing_interval': 0.1,
            'coalition_formation_threshold': 0.6,
            'learning_feedback_enabled': True
        }
        
        # 性能统计
        self.performance_stats = {
            'total_integrations': 0,
            'successful_integrations': 0,
            'reasoning_triggers': 0,
            'coalition_formations': 0,
            'activation_propagations': 0,
            'learning_updates': 0,
            'average_response_time': 0.0
        }
        
        # 线程控制
        self.lock = threading.RLock()
        self.running = False
        
        # 启动集成循环
        self._start_integration_loop()
    
    def _start_integration_loop(self):
        """启动集成处理循环"""
        def integration_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self.running = True
            loop.run_until_complete(self._integration_processor())
        
        integration_thread = threading.Thread(target=integration_loop, daemon=True)
        integration_thread.start()
    
    async def _integration_processor(self):
        """集成处理器主循环"""
        while self.running:
            try:
                # 处理认知事件
                await self._process_cognitive_events()
                
                # 更新激活传播
                await self._update_activation_propagation()
                
                # 检查推理触发条件
                await self._check_reasoning_triggers()
                
                # 管理联盟形成
                await self._manage_coalition_formation()
                
                # 执行学习更新
                await self._perform_learning_updates()
                
                # 清理过期上下文
                await self._cleanup_expired_contexts()
                
                await asyncio.sleep(self.config['event_processing_interval'])
                
            except Exception as e:
                self.logger.error(f"Error in integration processor: {e}")
                await asyncio.sleep(1.0)
    
    async def create_integration_context(self, mode: IntegrationMode = None, 
                                       activation_flow: ActivationFlow = None) -> str:
        """创建集成上下文"""
        if mode is None:
            mode = self.config['integration_mode']
        if activation_flow is None:
            activation_flow = ActivationFlow.BIDIRECTIONAL
        
        context_id = f"ctx_{uuid.uuid4().hex[:8]}"
        
        with self.lock:
            if len(self.integration_contexts) >= self.config['max_concurrent_contexts']:
                # 清理最旧的上下文
                oldest_context = min(self.integration_contexts.values(), 
                                   key=lambda ctx: ctx.created_time)
                del self.integration_contexts[oldest_context.context_id]
            
            context = IntegrationContext(
                context_id=context_id,
                mode=mode,
                activation_flow=activation_flow
            )
            self.integration_contexts[context_id] = context
        
        self.logger.info(f"Created integration context: {context_id}")
        return context_id
    
    async def submit_cognitive_event(self, event: CognitiveEvent):
        """提交认知事件"""
        with self.lock:
            self.cognitive_events.append(event)
        
        self.logger.debug(f"Submitted cognitive event: {event.event_id}")
    
    async def _process_cognitive_events(self):
        """处理认知事件"""
        events_to_process = []
        
        with self.lock:
            # 获取未处理的事件
            events_to_process = [event for event in self.cognitive_events if not event.processed]
        
        for event in events_to_process:
            try:
                await self._process_single_event(event)
                event.mark_processed()
                
                # 更新统计
                self.performance_stats['total_integrations'] += 1
                
            except Exception as e:
                self.logger.error(f"Error processing event {event.event_id}: {e}")
    
    async def _process_single_event(self, event: CognitiveEvent):
        """处理单个认知事件"""
        start_time = time.time()
        
        try:
            # 根据事件类型选择处理策略
            if event.source_module == 'NARS' and event.target_module == 'LIDA':
                await self._process_nars_to_lida_event(event)
            elif event.source_module == 'LIDA' and event.target_module == 'NARS':
                await self._process_lida_to_nars_event(event)
            else:
                await self._process_bidirectional_event(event)
            
            # 更新激活网络
            self._update_activation_network(event)
            
            # 记录成功处理
            self.performance_stats['successful_integrations'] += 1
            
        except Exception as e:
            self.logger.error(f"Failed to process event {event.event_id}: {e}")
        
        finally:
            # 更新响应时间统计
            processing_time = time.time() - start_time
            self._update_response_time_stats(processing_time)
    
    async def _process_nars_to_lida_event(self, event: CognitiveEvent):
        """处理NARS到LIDA的事件"""
        # 将NARS推理结果转换为LIDA工作空间内容
        if isinstance(event.data, (Task, Sentence)):
            workspace_content = await self._convert_nars_to_workspace_content(event.data)
            if workspace_content and self.lida_agent:
                # 添加到工作空间
                await self._add_to_workspace(workspace_content, event.activation)
    
    async def _process_lida_to_nars_event(self, event: CognitiveEvent):
        """处理LIDA到NARS的事件"""
        # 将LIDA工作空间内容转换为NARS任务
        if isinstance(event.data, (Node, WorkspaceContent)):
            nars_task = await self._convert_lida_to_nars_task(event.data)
            if nars_task and self.reasoning_engine:
                # 提交推理请求
                await self._submit_reasoning_request(nars_task, event.confidence)
    
    async def _process_bidirectional_event(self, event: CognitiveEvent):
        """处理双向事件"""
        # 同时触发两个方向的处理
        await self._process_nars_to_lida_event(event)
        await self._process_lida_to_nars_event(event)
    
    def _update_activation_network(self, event: CognitiveEvent):
        """更新激活网络"""
        source_key = f"{event.source_module}_{event.event_type}"
        target_key = f"{event.target_module}_{event.event_type}"
        
        # 更新激活连接强度
        current_strength = self.activation_network[source_key][target_key]
        new_strength = current_strength * 0.9 + event.activation * 0.1
        self.activation_network[source_key][target_key] = new_strength
        
        self.performance_stats['activation_propagations'] += 1
    
    def _update_response_time_stats(self, processing_time: float):
        """更新响应时间统计"""
        current_avg = self.performance_stats['average_response_time']
        total_integrations = self.performance_stats['total_integrations']
        
        if total_integrations > 0:
            self.performance_stats['average_response_time'] = (
                (current_avg * (total_integrations - 1) + processing_time) / total_integrations
            )
        else:
            self.performance_stats['average_response_time'] = processing_time
    
    async def _update_activation_propagation(self):
        """更新激活传播"""
        # 应用激活衰减
        for source in self.activation_network:
            for target in self.activation_network[source]:
                self.activation_network[source][target] *= self.config['activation_decay_rate']
    
    async def _check_reasoning_triggers(self):
        """检查推理触发条件"""
        # 检查是否有足够的激活来触发推理
        for context_id, context in self.integration_contexts.items():
            avg_activation = context.get_average_activation()
            
            if avg_activation > self.activation_thresholds['reasoning_trigger']:
                await self._trigger_reasoning(context)
                self.performance_stats['reasoning_triggers'] += 1
    
    async def _trigger_reasoning(self, context: IntegrationContext):
        """触发推理过程"""
        if not self.reasoning_engine or not context.nars_elements:
            return
        
        try:
            # 创建推理请求
            request = ReasoningRequest(
                request_id=f"reasoning_{context.context_id}_{uuid.uuid4().hex[:8]}",
                premises=context.nars_elements[:5],  # 限制前提数量
                strategy=ReasoningStrategy.ADAPTIVE,
                priority=ReasoningPriority.MEDIUM,
                timeout=self.config['reasoning_timeout']
            )
            
            # 提交推理请求
            request_id = await self.reasoning_engine.submit_reasoning_request(request)
            
            # 等待结果
            result = await self.reasoning_engine.get_reasoning_result(
                request_id, timeout=self.config['reasoning_timeout']
            )
            
            if result and result.success:
                # 将推理结果集成回LIDA
                await self._integrate_reasoning_result(context, result)
                
        except Exception as e:
            self.logger.error(f"Error triggering reasoning for context {context.context_id}: {e}")
    
    async def _manage_coalition_formation(self):
        """管理联盟形成"""
        # 基于激活水平形成认知联盟
        high_activation_elements = []
        
        for context in self.integration_contexts.values():
            for element_id, activation in context.activation_levels.items():
                if activation > self.config['coalition_formation_threshold']:
                    high_activation_elements.append((element_id, activation, context))
        
        if len(high_activation_elements) >= 2:
            await self._form_coalition(high_activation_elements)
            self.performance_stats['coalition_formations'] += 1
    
    async def _form_coalition(self, elements: List[Tuple]):
        """形成认知联盟"""
        coalition_id = f"coalition_{uuid.uuid4().hex[:8]}"
        
        # 创建联盟对象（简化实现）
        coalition_data = {
            'coalition_id': coalition_id,
            'elements': elements,
            'formed_time': time.time(),
            'total_activation': sum(elem[1] for elem in elements)
        }
        
        self.active_coalitions[coalition_id] = coalition_data
        self.logger.info(f"Formed coalition: {coalition_id} with {len(elements)} elements")
    
    async def _perform_learning_updates(self):
        """执行学习更新"""
        if not self.config['learning_feedback_enabled'] or not self.learning_system:
            return
        
        # 收集最近的推理结果进行学习
        # 这里需要从推理引擎获取结果
        # 暂时跳过具体实现
        self.performance_stats['learning_updates'] += 1
    
    async def _cleanup_expired_contexts(self):
        """清理过期上下文"""
        current_time = time.time()
        expired_contexts = []
        
        with self.lock:
            for context_id, context in self.integration_contexts.items():
                if current_time - context.last_updated > 300:  # 5分钟过期
                    expired_contexts.append(context_id)
        
        for context_id in expired_contexts:
            with self.lock:
                if context_id in self.integration_contexts:
                    del self.integration_contexts[context_id]
            self.logger.debug(f"Cleaned up expired context: {context_id}")
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        with self.lock:
            return {
                'performance_stats': self.performance_stats.copy(),
                'active_contexts': len(self.integration_contexts),
                'active_coalitions': len(self.active_coalitions),
                'cognitive_events': len(self.cognitive_events),
                'activation_network_size': sum(len(targets) for targets in self.activation_network.values()),
                'config': self.config.copy()
            }
    
    def shutdown(self):
        """关闭集成系统"""
        self.running = False
        self.logger.info("Enhanced NARS-LIDA integration system shutdown")


# 全局集成系统实例
enhanced_integration_system = None

def get_enhanced_integration_system(nar_instance=None, lida_agent=None) -> EnhancedNarsLidaIntegration:
    """获取增强集成系统实例"""
    global enhanced_integration_system
    if enhanced_integration_system is None:
        enhanced_integration_system = EnhancedNarsLidaIntegration(nar_instance, lida_agent)
    return enhanced_integration_system
