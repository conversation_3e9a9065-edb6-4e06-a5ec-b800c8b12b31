"""
增强版NARS推理引擎
整合了优化的三段论推理、真值计算和概念处理功能

主要特性：
1. 统一的推理接口
2. 推理路径追踪和分析
3. 动态推理策略选择
4. 性能监控和优化
5. 与LIDA系统的深度集成
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import threading

from .syllogistic_rules import SyllogisticRules, ReasoningPath, ReasoningStats
from .truth_functions import TruthFunctions, TruthCalculationContext
from .budget_functions import BudgetFunctions
from ..entity.task import Task
from ..entity.sentence import Sentence
from ..entity.truth_value import TruthValue
from ..entity.budget_value import BudgetValue
from ..control.derivation_context import DerivationContext


class ReasoningStrategy(Enum):
    """推理策略枚举"""
    CONSERVATIVE = "conservative"    # 保守策略，高置信度要求
    AGGRESSIVE = "aggressive"       # 激进策略，低置信度要求
    BALANCED = "balanced"           # 平衡策略，中等置信度要求
    ADAPTIVE = "adaptive"           # 自适应策略，动态调整


class ReasoningPriority(Enum):
    """推理优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ReasoningRequest:
    """推理请求"""
    request_id: str
    premises: List[Sentence]
    target_terms: List[Any] = field(default_factory=list)
    strategy: ReasoningStrategy = ReasoningStrategy.BALANCED
    priority: ReasoningPriority = ReasoningPriority.MEDIUM
    max_steps: int = 10
    timeout: float = 5.0
    context_data: Dict[str, Any] = field(default_factory=dict)
    created_time: float = field(default_factory=time.time)


@dataclass
class ReasoningResult:
    """推理结果"""
    request_id: str
    conclusions: List[Task]
    reasoning_paths: List[ReasoningPath]
    confidence: float
    execution_time: float
    strategy_used: ReasoningStrategy
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class EnhancedReasoningEngine:
    """增强版NARS推理引擎"""
    
    def __init__(self, nar_instance=None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.nar = nar_instance
        
        # 推理队列和处理器
        self.reasoning_queue = asyncio.Queue()
        self.active_requests: Dict[str, ReasoningRequest] = {}
        self.completed_results: Dict[str, ReasoningResult] = {}
        
        # 推理统计和监控
        self.global_stats = ReasoningStats()
        self.strategy_performance: Dict[ReasoningStrategy, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        
        # 推理参数配置
        self.config = {
            'max_concurrent_requests': 10,
            'default_timeout': 10.0,
            'confidence_threshold': 0.1,
            'max_reasoning_depth': 15,
            'cache_enabled': True,
            'performance_monitoring': True
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 启动推理处理器
        self._start_reasoning_processor()
    
    def _start_reasoning_processor(self):
        """启动推理处理器"""
        def processor():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self._reasoning_processor())
        
        processor_thread = threading.Thread(target=processor, daemon=True)
        processor_thread.start()
    
    async def _reasoning_processor(self):
        """推理处理器主循环"""
        while True:
            try:
                # 获取推理请求
                request = await self.reasoning_queue.get()
                
                # 处理推理请求
                result = await self._process_reasoning_request(request)
                
                # 存储结果
                with self.lock:
                    self.completed_results[request.request_id] = result
                    if request.request_id in self.active_requests:
                        del self.active_requests[request.request_id]
                
                # 更新统计信息
                self._update_strategy_performance(request.strategy, result)
                
            except Exception as e:
                self.logger.error(f"Error in reasoning processor: {e}")
                await asyncio.sleep(0.1)
    
    async def submit_reasoning_request(self, request: ReasoningRequest) -> str:
        """提交推理请求"""
        with self.lock:
            if len(self.active_requests) >= self.config['max_concurrent_requests']:
                raise RuntimeError("Too many concurrent reasoning requests")
            
            self.active_requests[request.request_id] = request
        
        await self.reasoning_queue.put(request)
        return request.request_id
    
    async def get_reasoning_result(self, request_id: str, timeout: float = None) -> Optional[ReasoningResult]:
        """获取推理结果"""
        if timeout is None:
            timeout = self.config['default_timeout']
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            with self.lock:
                if request_id in self.completed_results:
                    return self.completed_results.pop(request_id)
            
            await asyncio.sleep(0.1)
        
        return None
    
    async def _process_reasoning_request(self, request: ReasoningRequest) -> ReasoningResult:
        """处理推理请求"""
        start_time = time.time()
        
        try:
            # 选择推理策略
            strategy = self._select_optimal_strategy(request)
            
            # 执行推理
            conclusions = []
            reasoning_paths = []
            
            if len(request.premises) >= 2:
                # 执行三段论推理
                syllogistic_results = await self._perform_syllogistic_reasoning(
                    request.premises, strategy, request.max_steps)
                conclusions.extend(syllogistic_results['conclusions'])
                reasoning_paths.extend(syllogistic_results['paths'])
            
            # 计算总体置信度
            total_confidence = 0.0
            if conclusions:
                confidences = [c.sentence.truth.get_confidence() for c in conclusions 
                             if c.sentence and c.sentence.truth]
                total_confidence = sum(confidences) / len(confidences) if confidences else 0.0
            
            execution_time = time.time() - start_time
            
            return ReasoningResult(
                request_id=request.request_id,
                conclusions=conclusions,
                reasoning_paths=reasoning_paths,
                confidence=total_confidence,
                execution_time=execution_time,
                strategy_used=strategy,
                success=len(conclusions) > 0,
                metadata={
                    'premise_count': len(request.premises),
                    'reasoning_steps': sum(len(path.steps) for path in reasoning_paths),
                    'strategy_selected': strategy.value
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error processing reasoning request {request.request_id}: {e}")
            return ReasoningResult(
                request_id=request.request_id,
                conclusions=[],
                reasoning_paths=[],
                confidence=0.0,
                execution_time=time.time() - start_time,
                strategy_used=request.strategy,
                success=False,
                error_message=str(e)
            )
    
    def _select_optimal_strategy(self, request: ReasoningRequest) -> ReasoningStrategy:
        """选择最优推理策略"""
        if request.strategy != ReasoningStrategy.ADAPTIVE:
            return request.strategy
        
        # 基于历史性能选择策略
        best_strategy = ReasoningStrategy.BALANCED
        best_score = 0.0
        
        for strategy, performance in self.strategy_performance.items():
            if performance['total_requests'] > 0:
                success_rate = performance['successful_requests'] / performance['total_requests']
                avg_confidence = performance['total_confidence'] / performance['total_requests']
                score = success_rate * 0.7 + avg_confidence * 0.3
                
                if score > best_score:
                    best_score = score
                    best_strategy = strategy
        
        return best_strategy
    
    async def _perform_syllogistic_reasoning(self, premises: List[Sentence], 
                                           strategy: ReasoningStrategy, 
                                           max_steps: int) -> Dict[str, List]:
        """执行三段论推理"""
        conclusions = []
        reasoning_paths = []
        
        # 这里需要实现具体的三段论推理逻辑
        # 暂时返回空结果
        return {
            'conclusions': conclusions,
            'paths': reasoning_paths
        }
    
    def _update_strategy_performance(self, strategy: ReasoningStrategy, result: ReasoningResult):
        """更新策略性能统计"""
        with self.lock:
            perf = self.strategy_performance[strategy]
            perf['total_requests'] += 1
            perf['total_confidence'] += result.confidence
            perf['total_execution_time'] += result.execution_time
            
            if result.success:
                perf['successful_requests'] += 1
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取推理引擎统计信息"""
        with self.lock:
            return {
                'global_stats': self.global_stats,
                'strategy_performance': dict(self.strategy_performance),
                'active_requests': len(self.active_requests),
                'completed_results': len(self.completed_results),
                'syllogistic_stats': SyllogisticRules.get_stats()
            }
    
    def reset_statistics(self):
        """重置统计信息"""
        with self.lock:
            self.global_stats = ReasoningStats()
            self.strategy_performance.clear()
            SyllogisticRules.reset_stats()


# 全局推理引擎实例
enhanced_reasoning_engine = None

def get_enhanced_reasoning_engine(nar_instance=None) -> EnhancedReasoningEngine:
    """获取增强推理引擎实例"""
    global enhanced_reasoning_engine
    if enhanced_reasoning_engine is None:
        enhanced_reasoning_engine = EnhancedReasoningEngine(nar_instance)
    return enhanced_reasoning_engine
