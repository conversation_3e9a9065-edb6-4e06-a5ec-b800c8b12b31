"""
增强版变量绑定与参数传递系统
优化变量绑定系统，支持更复杂的推理过程中的变量管理和参数传递

主要特性：
1. 高效的变量绑定算法
2. 上下文感知的变量作用域管理
3. 复杂推理链中的变量传递
4. 变量约束和类型检查
5. 绑定冲突检测和解决
6. 性能优化和缓存机制
"""

import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple, TypeVar
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import threading
import weakref
from abc import ABC, abstractmethod

# NARS语言相关导入
try:
    from linars.org.opennars.language.variable import Variable
    from linars.org.opennars.language.statement import Statement
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    from linars.edu.memphis.ccrg.linars.atomic_term import AtomicTerm
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False
    # 创建模拟类
    class Variable: pass
    class Statement: pass
    class Term: pass
    class CompoundTerm: pass
    class AtomicTerm: pass


class VariableType(Enum):
    """变量类型"""
    INDEPENDENT = "independent"    # 独立变量
    DEPENDENT = "dependent"       # 依赖变量
    QUERY = "query"              # 查询变量
    TEMPORAL = "temporal"        # 时序变量
    MODAL = "modal"             # 模态变量


class BindingScope(Enum):
    """绑定作用域"""
    LOCAL = "local"              # 局部作用域
    GLOBAL = "global"           # 全局作用域
    CONTEXT = "context"         # 上下文作用域
    INFERENCE = "inference"     # 推理作用域


class BindingStatus(Enum):
    """绑定状态"""
    UNBOUND = "unbound"         # 未绑定
    BOUND = "bound"             # 已绑定
    PARTIALLY_BOUND = "partially_bound"  # 部分绑定
    CONFLICTED = "conflicted"   # 冲突绑定


@dataclass
class VariableBinding:
    """变量绑定"""
    variable: Any
    value: Any
    binding_type: VariableType
    scope: BindingScope
    confidence: float = 1.0
    created_time: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)
    usage_count: int = 0
    constraints: List[Callable] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_valid(self) -> bool:
        """检查绑定是否有效"""
        try:
            # 检查所有约束
            for constraint in self.constraints:
                if not constraint(self.value):
                    return False
            return True
        except Exception:
            return False
    
    def update_usage(self):
        """更新使用统计"""
        self.usage_count += 1
        self.last_used = time.time()


@dataclass
class BindingContext:
    """绑定上下文"""
    context_id: str
    parent_context: Optional['BindingContext'] = None
    bindings: Dict[str, VariableBinding] = field(default_factory=dict)
    scope_level: int = 0
    created_time: float = field(default_factory=time.time)
    active: bool = True
    
    def get_binding(self, variable_name: str) -> Optional[VariableBinding]:
        """获取变量绑定"""
        # 首先在当前上下文中查找
        if variable_name in self.bindings:
            binding = self.bindings[variable_name]
            binding.update_usage()
            return binding
        
        # 在父上下文中查找
        if self.parent_context:
            return self.parent_context.get_binding(variable_name)
        
        return None
    
    def set_binding(self, variable_name: str, binding: VariableBinding):
        """设置变量绑定"""
        self.bindings[variable_name] = binding
    
    def has_binding(self, variable_name: str) -> bool:
        """检查是否有绑定"""
        return self.get_binding(variable_name) is not None


class VariableConstraint(ABC):
    """变量约束抽象基类"""
    
    @abstractmethod
    def check(self, value: Any) -> bool:
        """检查约束"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取约束描述"""
        pass


class TypeConstraint(VariableConstraint):
    """类型约束"""
    
    def __init__(self, expected_type: type):
        self.expected_type = expected_type
    
    def check(self, value: Any) -> bool:
        return isinstance(value, self.expected_type)
    
    def get_description(self) -> str:
        return f"Type must be {self.expected_type.__name__}"


class RangeConstraint(VariableConstraint):
    """范围约束"""
    
    def __init__(self, min_value: Any = None, max_value: Any = None):
        self.min_value = min_value
        self.max_value = max_value
    
    def check(self, value: Any) -> bool:
        try:
            if self.min_value is not None and value < self.min_value:
                return False
            if self.max_value is not None and value > self.max_value:
                return False
            return True
        except TypeError:
            return False
    
    def get_description(self) -> str:
        return f"Value must be between {self.min_value} and {self.max_value}"


class EnhancedVariableBindingSystem:
    """增强版变量绑定系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 绑定上下文管理
        self.contexts: Dict[str, BindingContext] = {}
        self.active_context: Optional[BindingContext] = None
        self.context_stack: List[BindingContext] = []
        
        # 全局绑定表
        self.global_bindings: Dict[str, VariableBinding] = {}
        
        # 绑定缓存
        self.binding_cache: Dict[str, VariableBinding] = {}
        self.cache_stats = {'hits': 0, 'misses': 0}
        
        # 约束管理
        self.global_constraints: Dict[str, List[VariableConstraint]] = defaultdict(list)
        
        # 配置参数
        self.config = {
            'max_contexts': 100,
            'cache_size_limit': 1000,
            'binding_timeout': 300.0,  # 5分钟
            'conflict_resolution_strategy': 'latest_wins',
            'enable_type_checking': True,
            'enable_constraint_checking': True
        }
        
        # 性能统计
        self.performance_stats = {
            'total_bindings': 0,
            'successful_bindings': 0,
            'failed_bindings': 0,
            'constraint_violations': 0,
            'context_switches': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'average_binding_time': 0.0
        }
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 创建根上下文
        self._create_root_context()
    
    def _create_root_context(self):
        """创建根上下文"""
        root_context = BindingContext(
            context_id="root",
            scope_level=0
        )
        
        with self.lock:
            self.contexts["root"] = root_context
            self.active_context = root_context
            self.context_stack = [root_context]
    
    def create_context(self, context_id: str = None, 
                      parent_context: BindingContext = None) -> str:
        """创建新的绑定上下文"""
        if context_id is None:
            context_id = f"ctx_{uuid.uuid4().hex[:8]}"
        
        if parent_context is None:
            parent_context = self.active_context
        
        with self.lock:
            if len(self.contexts) >= self.config['max_contexts']:
                # 清理最旧的上下文
                self._cleanup_old_contexts()
            
            new_context = BindingContext(
                context_id=context_id,
                parent_context=parent_context,
                scope_level=parent_context.scope_level + 1 if parent_context else 0
            )
            
            self.contexts[context_id] = new_context
        
        self.logger.debug(f"Created binding context: {context_id}")
        return context_id
    
    def switch_context(self, context_id: str) -> bool:
        """切换到指定上下文"""
        with self.lock:
            if context_id not in self.contexts:
                self.logger.error(f"Context not found: {context_id}")
                return False
            
            new_context = self.contexts[context_id]
            if not new_context.active:
                self.logger.error(f"Context is not active: {context_id}")
                return False
            
            self.active_context = new_context
            if new_context not in self.context_stack:
                self.context_stack.append(new_context)
            
            self.performance_stats['context_switches'] += 1
            self.logger.debug(f"Switched to context: {context_id}")
            return True
    
    def push_context(self, context_id: str = None) -> str:
        """推入新上下文到栈顶"""
        if context_id is None:
            context_id = self.create_context()
        elif context_id not in self.contexts:
            self.create_context(context_id)
        
        with self.lock:
            context = self.contexts[context_id]
            self.context_stack.append(context)
            self.active_context = context
        
        return context_id
    
    def pop_context(self) -> Optional[str]:
        """弹出栈顶上下文"""
        with self.lock:
            if len(self.context_stack) <= 1:  # 保留根上下文
                return None
            
            popped_context = self.context_stack.pop()
            self.active_context = self.context_stack[-1]
            
            return popped_context.context_id
    
    def bind_variable(self, variable_name: str, value: Any, 
                     variable_type: VariableType = VariableType.INDEPENDENT,
                     scope: BindingScope = BindingScope.CONTEXT,
                     confidence: float = 1.0,
                     constraints: List[VariableConstraint] = None) -> bool:
        """绑定变量"""
        start_time = time.time()
        
        try:
            # 检查约束
            if constraints is None:
                constraints = []
            
            # 添加全局约束
            if variable_name in self.global_constraints:
                constraints.extend(self.global_constraints[variable_name])
            
            # 验证约束
            if self.config['enable_constraint_checking']:
                for constraint in constraints:
                    if not constraint.check(value):
                        self.logger.warning(f"Constraint violation for variable {variable_name}: {constraint.get_description()}")
                        self.performance_stats['constraint_violations'] += 1
                        if self.config['enable_type_checking']:
                            return False
            
            # 创建绑定
            binding = VariableBinding(
                variable=variable_name,
                value=value,
                binding_type=variable_type,
                scope=scope,
                confidence=confidence,
                constraints=[c.check for c in constraints]
            )
            
            # 根据作用域设置绑定
            with self.lock:
                if scope == BindingScope.GLOBAL:
                    self.global_bindings[variable_name] = binding
                elif scope == BindingScope.CONTEXT and self.active_context:
                    self.active_context.set_binding(variable_name, binding)
                else:
                    # 默认设置到当前上下文
                    if self.active_context:
                        self.active_context.set_binding(variable_name, binding)
                
                # 更新缓存
                cache_key = f"{variable_name}_{scope.value}"
                self.binding_cache[cache_key] = binding
                
                # 清理缓存大小
                if len(self.binding_cache) > self.config['cache_size_limit']:
                    self._cleanup_cache()
            
            # 更新统计
            self.performance_stats['total_bindings'] += 1
            self.performance_stats['successful_bindings'] += 1
            
            binding_time = time.time() - start_time
            self._update_average_binding_time(binding_time)
            
            self.logger.debug(f"Successfully bound variable: {variable_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error binding variable {variable_name}: {e}")
            self.performance_stats['failed_bindings'] += 1
            return False
    
    def get_binding(self, variable_name: str, 
                   scope: BindingScope = None) -> Optional[VariableBinding]:
        """获取变量绑定"""
        # 检查缓存
        if scope:
            cache_key = f"{variable_name}_{scope.value}"
            if cache_key in self.binding_cache:
                self.performance_stats['cache_hits'] += 1
                return self.binding_cache[cache_key]
        
        self.performance_stats['cache_misses'] += 1
        
        with self.lock:
            # 根据作用域查找
            if scope == BindingScope.GLOBAL:
                return self.global_bindings.get(variable_name)
            elif scope == BindingScope.CONTEXT and self.active_context:
                return self.active_context.get_binding(variable_name)
            else:
                # 按优先级查找：上下文 -> 全局
                if self.active_context:
                    binding = self.active_context.get_binding(variable_name)
                    if binding:
                        return binding
                
                return self.global_bindings.get(variable_name)
    
    def unbind_variable(self, variable_name: str, 
                       scope: BindingScope = None) -> bool:
        """解绑变量"""
        try:
            with self.lock:
                removed = False
                
                if scope == BindingScope.GLOBAL or scope is None:
                    if variable_name in self.global_bindings:
                        del self.global_bindings[variable_name]
                        removed = True
                
                if scope == BindingScope.CONTEXT or scope is None:
                    if self.active_context and variable_name in self.active_context.bindings:
                        del self.active_context.bindings[variable_name]
                        removed = True
                
                # 清理缓存
                cache_keys_to_remove = [key for key in self.binding_cache.keys() 
                                      if key.startswith(f"{variable_name}_")]
                for key in cache_keys_to_remove:
                    del self.binding_cache[key]
                
                if removed:
                    self.logger.debug(f"Unbound variable: {variable_name}")
                
                return removed
                
        except Exception as e:
            self.logger.error(f"Error unbinding variable {variable_name}: {e}")
            return False
    
    def add_constraint(self, variable_name: str, constraint: VariableConstraint,
                      global_constraint: bool = False):
        """添加变量约束"""
        if global_constraint:
            self.global_constraints[variable_name].append(constraint)
        else:
            # 添加到当前绑定的约束中
            binding = self.get_binding(variable_name)
            if binding:
                binding.constraints.append(constraint.check)
    
    def resolve_conflicts(self, variable_name: str) -> bool:
        """解决绑定冲突"""
        try:
            # 获取所有可能的绑定
            bindings = []
            
            # 全局绑定
            if variable_name in self.global_bindings:
                bindings.append(('global', self.global_bindings[variable_name]))
            
            # 上下文绑定
            if self.active_context:
                context_binding = self.active_context.get_binding(variable_name)
                if context_binding:
                    bindings.append(('context', context_binding))
            
            if len(bindings) <= 1:
                return True  # 没有冲突
            
            # 根据策略解决冲突
            strategy = self.config['conflict_resolution_strategy']
            
            if strategy == 'latest_wins':
                # 保留最新的绑定
                latest_binding = max(bindings, key=lambda x: x[1].created_time)
                self._keep_binding_only(variable_name, latest_binding)
            elif strategy == 'highest_confidence':
                # 保留置信度最高的绑定
                best_binding = max(bindings, key=lambda x: x[1].confidence)
                self._keep_binding_only(variable_name, best_binding)
            elif strategy == 'context_priority':
                # 上下文绑定优先
                context_bindings = [b for b in bindings if b[0] == 'context']
                if context_bindings:
                    self._keep_binding_only(variable_name, context_bindings[0])
                else:
                    self._keep_binding_only(variable_name, bindings[0])
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error resolving conflicts for variable {variable_name}: {e}")
            return False
    
    def _keep_binding_only(self, variable_name: str, binding_to_keep: Tuple[str, VariableBinding]):
        """只保留指定的绑定"""
        scope_type, binding = binding_to_keep
        
        with self.lock:
            # 清除其他绑定
            if scope_type != 'global' and variable_name in self.global_bindings:
                del self.global_bindings[variable_name]
            
            if scope_type != 'context' and self.active_context:
                if variable_name in self.active_context.bindings:
                    del self.active_context.bindings[variable_name]
            
            # 设置保留的绑定
            if scope_type == 'global':
                self.global_bindings[variable_name] = binding
            elif scope_type == 'context' and self.active_context:
                self.active_context.set_binding(variable_name, binding)
    
    def _cleanup_old_contexts(self):
        """清理旧的上下文"""
        current_time = time.time()
        timeout = self.config['binding_timeout']
        
        contexts_to_remove = []
        for context_id, context in self.contexts.items():
            if (context_id != "root" and 
                current_time - context.created_time > timeout and
                context not in self.context_stack):
                contexts_to_remove.append(context_id)
        
        for context_id in contexts_to_remove:
            del self.contexts[context_id]
            self.logger.debug(f"Cleaned up old context: {context_id}")
    
    def _cleanup_cache(self):
        """清理绑定缓存"""
        # 移除最旧的缓存项
        cache_items = list(self.binding_cache.items())
        cache_items.sort(key=lambda x: x[1].last_used)
        
        # 保留最近使用的一半
        keep_count = len(cache_items) // 2
        self.binding_cache = dict(cache_items[-keep_count:])
    
    def _update_average_binding_time(self, binding_time: float):
        """更新平均绑定时间"""
        current_avg = self.performance_stats['average_binding_time']
        total_bindings = self.performance_stats['total_bindings']
        
        if total_bindings > 1:
            self.performance_stats['average_binding_time'] = (
                (current_avg * (total_bindings - 1) + binding_time) / total_bindings
            )
        else:
            self.performance_stats['average_binding_time'] = binding_time
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取绑定系统统计信息"""
        with self.lock:
            return {
                'performance_stats': self.performance_stats.copy(),
                'active_contexts': len(self.contexts),
                'context_stack_depth': len(self.context_stack),
                'global_bindings': len(self.global_bindings),
                'cache_size': len(self.binding_cache),
                'cache_hit_rate': (self.performance_stats['cache_hits'] / 
                                 max(1, self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'])),
                'current_context': self.active_context.context_id if self.active_context else None
            }
    
    def reset(self):
        """重置绑定系统"""
        with self.lock:
            self.contexts.clear()
            self.global_bindings.clear()
            self.binding_cache.clear()
            self.context_stack.clear()
            self.performance_stats = {key: 0 if isinstance(value, (int, float)) else value 
                                    for key, value in self.performance_stats.items()}
            
            # 重新创建根上下文
            self._create_root_context()
        
        self.logger.info("Variable binding system reset")


# 全局变量绑定系统实例
enhanced_variable_binding_system = None

def get_enhanced_variable_binding_system() -> EnhancedVariableBindingSystem:
    """获取增强变量绑定系统实例"""
    global enhanced_variable_binding_system
    if enhanced_variable_binding_system is None:
        enhanced_variable_binding_system = EnhancedVariableBindingSystem()
    return enhanced_variable_binding_system
