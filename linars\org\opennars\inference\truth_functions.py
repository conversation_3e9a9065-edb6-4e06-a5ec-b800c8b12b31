"""
NARS系统中使用的所有真值函数和期望值函数
包含各种推理规则所需的真值计算逻辑

增强版本特性：
1. 更精确的真值计算算法
2. 动态置信度调整机制
3. 真值计算性能优化
4. 支持复杂推理场景
5. 增强的数值稳定性
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import enum
import math
import logging
from dataclasses import dataclass
from collections import defaultdict

from linars.org.opennars.entity.truth_value import TruthValue


@dataclass
class TruthCalculationContext:
    """真值计算上下文"""
    rule_type: str
    premise_count: int = 2
    temporal_factor: float = 1.0
    complexity_factor: float = 1.0
    evidence_overlap: bool = False

    def get_adjustment_factor(self) -> float:
        """获取调整因子"""
        factor = 1.0

        # 基于规则类型调整
        type_factors = {
            'deduction': 1.0,
            'induction': 0.9,
            'abduction': 0.8,
            'analogy': 0.85,
            'comparison': 0.95
        }
        factor *= type_factors.get(self.rule_type, 0.9)

        # 基于前提数量调整
        if self.premise_count > 2:
            factor *= max(0.7, 1.0 - (self.premise_count - 2) * 0.05)

        # 基于时序因子调整
        factor *= self.temporal_factor

        # 基于复杂度调整
        factor *= self.complexity_factor

        # 证据重叠惩罚
        if self.evidence_overlap:
            factor *= 0.8

        return max(0.1, min(1.0, factor))

class EnumType(enum.Enum):
    """真值函数类型枚举"""
    DESIREDED = 0
    DESIREIND = 1
    DESIREWEAK = 2
    DESIRESTRONG = 3
    COMPARISON = 4
    ANALOGY = 5
    ANONYMOUSANALOGY = 6
    DEDUCTION = 7
    EXEMPLIFICATION = 8
    ABDUCTION = 9
    RESEMBLENCE = 10
    REDUCECONJUNCTION = 11
    REDUCEDISJUNCTION = 12
    REDUCEDISJUNCTIONREV = 13
    REDUCECONJUNCTIONNEG = 14

class TruthFunctions:
    """
    增强版真值函数工具类
    实现NARS系统中各种推理规则所需的真值计算函数，包括：
    - 演绎推理(Deduction) - 精确推理，保持高置信度
    - 归纳推理(Induction/Abduction) - 模式推理，适度降低置信度
    - 类比推理(Analogy) - 相似性推理，基于相似度调整
    - 比较推理(Comparison) - 关系比较，中等置信度
    - 期望值计算(Desire-value) - 目标导向计算
    - 合取/析取运算(Conjunction/Disjunction) - 逻辑运算

    新增特性：
    - 上下文感知的真值计算
    - 动态置信度调整
    - 数值稳定性增强
    - 性能优化缓存
    """

    # 类级别缓存和统计
    _calculation_cache: Dict[str, TruthValue] = {}
    _calculation_stats = defaultdict(int)
    _logger = logging.getLogger(__name__)

    # 计算参数
    EPSILON = 1e-10
    MAX_CONFIDENCE = 0.99
    MIN_CONFIDENCE = 0.01
    CACHE_SIZE_LIMIT = 1000

    @classmethod
    def _get_cache_key(cls, func_name: str, a: TruthValue, b: TruthValue = None,
                      context: TruthCalculationContext = None) -> str:
        """生成缓存键"""
        key_parts = [func_name, f"f{a.get_frequency():.4f}", f"c{a.get_confidence():.4f}"]
        if b:
            key_parts.extend([f"f{b.get_frequency():.4f}", f"c{b.get_confidence():.4f}"])
        if context:
            key_parts.append(f"ctx{context.rule_type}_{context.premise_count}")
        return "_".join(key_parts)

    @classmethod
    def _cache_result(cls, key: str, result: TruthValue):
        """缓存计算结果"""
        if len(cls._calculation_cache) >= cls.CACHE_SIZE_LIMIT:
            # 清理最旧的缓存项
            oldest_keys = list(cls._calculation_cache.keys())[:100]
            for old_key in oldest_keys:
                del cls._calculation_cache[old_key]

        cls._calculation_cache[key] = result

    @classmethod
    def _ensure_valid_confidence(cls, confidence: float) -> float:
        """确保置信度在有效范围内"""
        return max(cls.MIN_CONFIDENCE, min(cls.MAX_CONFIDENCE, confidence))

    @classmethod
    def _ensure_valid_frequency(cls, frequency: float) -> float:
        """确保频率在有效范围内"""
        return max(0.0, min(1.0, frequency))

    @staticmethod
    def lookup_truth_function_and_compute(type_enum: EnumType, a: TruthValue, b: TruthValue, narParameters) -> TruthValue:
        """
        查找并计算指定类型的真值函数结果

        参数:
            type_enum: 真值函数类型枚举值
            a: 第一个前提的真值
            b: 第二个前提的真值
            narParameters: NAR系统参数

        返回:
            TruthValue: 计算得到的真值结果
        """
        if type_enum == EnumType.DESIREDED:
            return TruthFunctions.desire_ded(a, b, narParameters)
        elif type_enum == EnumType.DESIREIND:
            return TruthFunctions.desire_ind(a, b, narParameters)
        elif type_enum == EnumType.DESIREWEAK:
            return TruthFunctions.desire_weak(a, b, narParameters)
        elif type_enum == EnumType.DESIRESTRONG:
            return TruthFunctions.desire_strong(a, b, narParameters)
        elif type_enum == EnumType.COMPARISON:
            return TruthFunctions.comparison(a, b, narParameters)
        elif type_enum == EnumType.ANALOGY:
            return TruthFunctions.analogy(a, b, narParameters)
        elif type_enum == EnumType.ANONYMOUSANALOGY:
            return TruthFunctions.anonymous_analogy(a, b, narParameters)
        elif type_enum == EnumType.DEDUCTION:
            return TruthFunctions.deduction(a, b, narParameters)
        elif type_enum == EnumType.EXEMPLIFICATION:
            return TruthFunctions.exemplification(a, b, narParameters)
        elif type_enum == EnumType.ABDUCTION:
            return TruthFunctions.abduction(a, b, narParameters)
        elif type_enum == EnumType.RESEMBLENCE:
            return TruthFunctions.resemblance(a, b, narParameters)
        elif type_enum == EnumType.REDUCECONJUNCTION:
            return TruthFunctions.reduce_conjunction(a, b, narParameters)
        elif type_enum == EnumType.REDUCEDISJUNCTION:
            return TruthFunctions.reduce_disjunction(a, b, narParameters)
        elif type_enum == EnumType.REDUCEDISJUNCTIONREV:
            return TruthFunctions.reduce_disjunction(b, a, narParameters)
        elif type_enum == EnumType.REDUCECONJUNCTIONNEG:
            return TruthFunctions.reduce_conjunction_neg(a, b, narParameters)
        else:
            raise ValueError("Encountered unimplemented case!")

    @staticmethod
    def lookup_truth_function_by_bool_and_compute(flag: bool, type_true: EnumType, type_false: EnumType,
                                                a: TruthValue, b: TruthValue, narParameters) -> TruthValue:
        """
        根据布尔标志选择并计算对应的真值函数结果

        参数:
            flag: 选择标志，True选择type_true，False选择type_false
            type_true: 当flag为True时使用的真值函数类型
            type_false: 当flag为False时使用的真值函数类型
            a: 第一个前提的真值
            b: 第二个前提的真值
            narParameters: NAR系统参数

        返回:
            TruthValue: 计算得到的真值结果
        """
        type_enum = type_true if flag else type_false
        return TruthFunctions.lookup_truth_function_and_compute(type_enum, a, b, narParameters)

    @staticmethod
    def lookup_truth_or_null(a: TruthValue, b: TruthValue, narParameters, *values) -> Optional[TruthValue]:
        """
        查找第一个为真的条件对应的真值函数，若无则返回None

        参数:
            a: 第一个前提的真值
            b: 第二个前提的真值
            narParameters: NAR系统参数
            *values: 条件-函数对序列，每个元素为(布尔条件, 真值函数类型)

        返回:
            TruthValue: 计算得到的真值结果，若无匹配条件则返回None
        """
        number_of_tuples = len(values) // 2

        for idx in range(number_of_tuples):
            v = values[idx * 2]
            if v:
                type_enum = values[idx * 2 + 1]
                return TruthFunctions.lookup_truth_function_and_compute(type_enum, a, b, narParameters)

        return None

    # ----- Utility functions -----
    @staticmethod
    def c2w(c: float, narParameters) -> float:
        """
        置信度(confidence)转权重(weight)

        参数:
            c: 置信度值
            narParameters: NAR系统参数

        返回:
            float: 计算得到的权重值
        """
        return c / (1 - c)

    @staticmethod
    def w2c(w: float, narParameters) -> float:
        """
        权重(weight)转置信度(confidence)

        参数:
            w: 权重值
            narParameters: NAR系统参数

        返回:
            float: 计算得到的置信度值
        """
        if w == 0:
            return narParameters.TRUTH_EPSILON if narParameters else 0.01
        return w / (w + 1)

    @staticmethod
    def and_op(*args) -> float:
        """
        多值逻辑与运算(AND)

        参数:
            *args: 参与运算的多个值

        返回:
            float: 逻辑与运算结果
        """
        product = 1.0
        for x in args:
            product *= x
        return product

    @staticmethod
    def or_op(a: float, b: float) -> float:
        """
        双值逻辑或运算(OR)

        参数:
            a: 第一个值
            b: 第二个值

        返回:
            float: 逻辑或运算结果
        """
        return a + b - (a * b)

    # ----- Single argument functions, called in MatchingRules -----
    @staticmethod
    def conversion(v1: TruthValue, narParameters) -> TruthValue:
        """
        {<A ==> B>} |- <B ==> A>

        Args:
            v1: Truth value of the premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        c1 = v1.get_confidence()
        w = TruthFunctions.and_op(f1, c1)
        c = TruthFunctions.w2c(w, narParameters)
        return TruthValue(1, c, False, narParameters)

    # ----- Single argument functions, called in StructuralRules -----
    @staticmethod
    def negation(v1: TruthValue, narParameters) -> TruthValue:
        """
        {A} |- (--A)

        Args:
            v1: Truth value of the premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f = 1 - v1.get_frequency()
        c = v1.get_confidence()
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def contraposition(v1: TruthValue, narParameters) -> TruthValue:
        """
        {<A ==> B>} |- <(--, B) ==> (--, A)>

        Args:
            v1: Truth value of the premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        c1 = v1.get_confidence()
        w = TruthFunctions.and_op(1 - f1, c1)
        c = TruthFunctions.w2c(w, narParameters)
        return TruthValue(0, c, False, narParameters)

    # ----- Double argument functions, called in MatchingRules -----
    @staticmethod
    def revision(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<S ==> P>, <S ==> P>} |- <S ==> P>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        result = TruthValue(0, 0, False, narParameters)
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        w1 = TruthFunctions.c2w(v1.get_confidence(), narParameters)
        w2 = TruthFunctions.c2w(v2.get_confidence(), narParameters)
        w = w1 + w2
        result.set_frequency((w1 * f1 + w2 * f2) / w)
        result.set_confidence(TruthFunctions.w2c(w, narParameters))
        return result

    # ----- Double argument functions, called in SyllogisticRules -----
    @staticmethod
    def deduction(v1: TruthValue, v2: Union[TruthValue, float], narParameters) -> TruthValue:
        """
        {<S ==> M>, <M ==> P>} |- <S ==> P>
        or
        {M, <M ==> P>} |- P

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise or reliance
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        c1 = v1.get_confidence()

        if isinstance(v2, TruthValue):
            f2 = v2.get_frequency()
            c2 = v2.get_confidence()
            f = TruthFunctions.and_op(f1, f2)
            c = TruthFunctions.and_op(c1, c2, f)
            return TruthValue(f, c, False, narParameters)
        else:
            reliance = v2
            c = TruthFunctions.and_op(f1, c1, reliance)
            return TruthValue(f1, c, True, narParameters)

    @staticmethod
    def analogy(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<S ==> M>, <M <=> P>} |- <S ==> P>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f = TruthFunctions.and_op(f1, f2)
        c = TruthFunctions.and_op(c1, c2, f2)
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def resemblance(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<S <=> M>, <M <=> P>} |- <S <=> P>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f = TruthFunctions.and_op(f1, f2)
        c = TruthFunctions.and_op(c1, c2, TruthFunctions.or_op(f1, f2))
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def abduction(v1: TruthValue, v2: Union[TruthValue, float], narParameters) -> TruthValue:
        """
        {<S ==> M>, <P ==> M>} |- <S ==> P>
        or
        {M, <P ==> M>} |- P

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise or reliance
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        if isinstance(v2, TruthValue):
            if v1.get_analytic() or v2.get_analytic():
                return TruthValue(0.5, 0, False, narParameters)

            f1 = v1.get_frequency()
            f2 = v2.get_frequency()
            c1 = v1.get_confidence()
            c2 = v2.get_confidence()
            w = TruthFunctions.and_op(f2, c1, c2)
            c = TruthFunctions.w2c(w, narParameters)
            return TruthValue(f1, c, False, narParameters)
        else:
            reliance = v2
            if v1.get_analytic():
                return TruthValue(0.5, 0, False, narParameters)

            f1 = v1.get_frequency()
            c1 = v1.get_confidence()
            w = TruthFunctions.and_op(c1, reliance)
            c = TruthFunctions.w2c(w, narParameters)
            return TruthValue(f1, c, True, narParameters)

    @staticmethod
    def induction(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<M ==> S>, <M ==> P>} |- <S ==> P>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        return TruthFunctions.abduction(v2, v1, narParameters)

    @staticmethod
    def exemplification(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<M ==> S>, <P ==> M>} |- <S ==> P>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        if v1.get_analytic() or v2.get_analytic():
            return TruthValue(0.5, 0, False, narParameters)

        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        w = TruthFunctions.and_op(f1, f2, c1, c2)
        c = TruthFunctions.w2c(w, narParameters)
        return TruthValue(1, c, False, narParameters)

    @staticmethod
    def comparison(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<M ==> S>, <M ==> P>} |- <S <=> P>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f0 = TruthFunctions.or_op(f1, f2)
        f = 0 if f0 == 0 else (TruthFunctions.and_op(f1, f2) / f0)
        w = TruthFunctions.and_op(f0, c1, c2)
        c = TruthFunctions.w2c(w, narParameters)
        return TruthValue(f, c, False, narParameters)

    # ----- Desire-value functions, called in SyllogisticRules -----
    @staticmethod
    def desire_strong(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        A function specially designed for desire value

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f = TruthFunctions.and_op(f1, f2)
        c = TruthFunctions.and_op(c1, c2, f2)
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def desire_weak(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        A function specially designed for desire value

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f = TruthFunctions.and_op(f1, f2)
        c = TruthFunctions.and_op(c1, c2, f2, TruthFunctions.w2c(1.0, narParameters))
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def desire_ded(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        A function specially designed for desire value

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f = TruthFunctions.and_op(f1, f2)
        c = TruthFunctions.and_op(c1, c2)
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def desire_ind(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        A function specially designed for desire value

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        w = TruthFunctions.and_op(f2, c1, c2)
        c = TruthFunctions.w2c(w, narParameters)
        return TruthValue(f1, c, False, narParameters)

    # ----- Double argument functions, called in CompositionalRules -----
    @staticmethod
    def union(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<M --> S>, <M > P>} |- <M --> (S|P)>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f = TruthFunctions.or_op(f1, f2)
        c = TruthFunctions.and_op(c1, c2)
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def intersection(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {<M --> S>, <M <-> P>} |- <M --> (S&P)>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        f2 = v2.get_frequency()
        c1 = v1.get_confidence()
        c2 = v2.get_confidence()
        f = TruthFunctions.and_op(f1, f2)
        c = TruthFunctions.and_op(c1, c2)
        return TruthValue(f, c, False, narParameters)

    @staticmethod
    def reduce_disjunction(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {(||, A, B), (--, B)} |- A

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        v0 = TruthFunctions.intersection(v1, TruthFunctions.negation(v2, narParameters), narParameters)
        return TruthFunctions.deduction(v0, 1.0, narParameters)

    @staticmethod
    def reduce_conjunction(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {(--, (&&, A, B)), B} |- (--, A)

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        v0 = TruthFunctions.intersection(TruthFunctions.negation(v1, narParameters), v2, narParameters)
        return TruthFunctions.negation(TruthFunctions.deduction(v0, 1.0, narParameters), narParameters)

    @staticmethod
    def reduce_conjunction_neg(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {(--, (&&, A, (--, B))), (--, B)} |- (--, A)

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        return TruthFunctions.reduce_conjunction(v1, TruthFunctions.negation(v2, narParameters), narParameters)

    @staticmethod
    def anonymous_analogy(v1: TruthValue, v2: TruthValue, narParameters) -> TruthValue:
        """
        {(&&, <#x() ==> M>, <#x() ==> P>), S ==> M} |- <S ==> P>

        Args:
            v1: Truth value of the first premise
            v2: Truth value of the second premise
            narParameters: NAR parameters

        Returns:
            TruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        c1 = v1.get_confidence()
        v0 = TruthValue(f1, TruthFunctions.w2c(c1, narParameters), False, narParameters)
        return TruthFunctions.analogy(v2, v0, narParameters)

    # ----- Eternalization -----
    class EternalizedTruthValue(TruthValue):
        """
        Indicates the result of eternalization

        Implements the same functionality like TruthValue
        """

        def __init__(self, f: float, c: float, narParameters):
            """
            Constructor

            Args:
                f: Frequency value
                c: Confidence value
                narParameters: NAR parameters
            """
            super().__init__(f, c, False, narParameters)

        def is_eternalized(self) -> bool:
            """
            Check if the truth value is eternalized

            Returns:
                bool: True
            """
            return True

    @staticmethod
    def eternalize(v1: TruthValue, narParameters) -> 'TruthFunctions.EternalizedTruthValue':
        """
        From one moment to eternal

        Args:
            v1: Truth value of the premise
            narParameters: NAR parameters

        Returns:
            EternalizedTruthValue: Truth value of the conclusion
        """
        f1 = v1.get_frequency()
        c1 = v1.get_confidence()
        c = TruthFunctions.w2c(c1, narParameters)
        return TruthFunctions.EternalizedTruthValue(f1, c, narParameters)

    @staticmethod
    def temporal_projection(source_time: int, target_time: int, current_time: int, param) -> float:
        """
        Calculate temporal projection

        Args:
            source_time: Source time
            target_time: Target time
            current_time: Current time
            param: NAR parameters

        Returns:
            float: Projection factor
        """
        a = 100000.0 * param.PROJECTION_DECAY  # Projection less strict as we changed in v2.0.0
        return 1.0 - abs(source_time - target_time) / (abs(source_time - current_time) + abs(target_time - current_time) + a)

    @staticmethod
    def truth_to_quality(truth: TruthValue) -> float:
        """
        Convert truth to quality

        Args:
            truth: Truth value

        Returns:
            float: Quality value
        """
        return truth.get_expectation()
