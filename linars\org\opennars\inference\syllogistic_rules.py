"""
三段论推理：基于关系传递性的推理规则集合。
包含各种基于NARS系统的演绎、归纳、类比等推理规则实现。

优化版本特性：
1. 增强的推理路径追踪
2. 动态置信度调整
3. 推理性能监控
4. 更精确的真值计算
5. 支持复杂推理链
"""
import time
import logging
from typing import List, Dict, Optional, Tuple, Set
from collections import defaultdict, deque
from dataclasses import dataclass, field

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.inference.budget_functions import BudgetFunctions
from linars.org.opennars.inference.truth_functions import TruthFunctions
from linars.org.opennars.inference.temporal_rules import TemporalRules
from linars.org.opennars.language.statement import Statement
from linars.org.opennars.language.inheritance import Inheritance
from linars.org.opennars.language.similarity import Similarity
from linars.org.opennars.language.implication import Implication
from linars.org.opennars.language.equivalence import Equivalence
from linars.org.opennars.language.conjunction import Conjunction
from linars.org.opennars.io.symbols import NativeOperator
from linars.org.opennars.language.variable import Variable


@dataclass
class ReasoningPath:
    """推理路径记录"""
    path_id: str
    steps: List[Dict] = field(default_factory=list)
    confidence: float = 0.0
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    success: bool = False

    def add_step(self, rule_type: str, premises: List, conclusion, truth_value: TruthValue):
        """添加推理步骤"""
        step = {
            'rule_type': rule_type,
            'premises': premises,
            'conclusion': conclusion,
            'truth_value': truth_value,
            'timestamp': time.time()
        }
        self.steps.append(step)

    def complete(self, success: bool = True):
        """完成推理路径"""
        self.end_time = time.time()
        self.success = success
        if self.steps:
            # 计算路径总体置信度
            confidences = [step['truth_value'].get_confidence() for step in self.steps if step['truth_value']]
            self.confidence = sum(confidences) / len(confidences) if confidences else 0.0


@dataclass
class ReasoningStats:
    """推理统计信息"""
    total_inferences: int = 0
    successful_inferences: int = 0
    failed_inferences: int = 0
    deduction_count: int = 0
    induction_count: int = 0
    abduction_count: int = 0
    analogy_count: int = 0
    average_inference_time: float = 0.0
    confidence_distribution: Dict[str, int] = field(default_factory=lambda: defaultdict(int))

    def update_inference(self, rule_type: str, success: bool, duration: float, confidence: float):
        """更新推理统计"""
        self.total_inferences += 1
        if success:
            self.successful_inferences += 1
        else:
            self.failed_inferences += 1

        # 更新规则类型计数
        if rule_type == 'deduction':
            self.deduction_count += 1
        elif rule_type == 'induction':
            self.induction_count += 1
        elif rule_type == 'abduction':
            self.abduction_count += 1
        elif rule_type == 'analogy':
            self.analogy_count += 1

        # 更新平均推理时间
        self.average_inference_time = (self.average_inference_time * (self.total_inferences - 1) + duration) / self.total_inferences

        # 更新置信度分布
        conf_range = f"{int(confidence * 10) * 10}-{int(confidence * 10) * 10 + 10}%"
        self.confidence_distribution[conf_range] += 1

class SyllogisticRules:
    """
    增强版三段论推理规则类
    实现NARS系统中基于关系传递性的各种推理规则，包括：
    - 演绎推理(Deduction) - 精确推理，高置信度
    - 归纳推理(Induction) - 模式发现，中等置信度
    - 溯因推理(Abduction) - 假设生成，低置信度
    - 类比推理(Analogy) - 相似性推理，可变置信度
    - 比较推理(Comparison) - 关系比较，中等置信度
    - 条件推理(Conditional) - 条件关系推理

    新增特性：
    - 推理路径追踪和统计
    - 动态置信度调整
    - 推理性能监控
    - 复杂推理链支持
    """

    # 类级别的统计信息
    _stats = ReasoningStats()
    _reasoning_paths: Dict[str, ReasoningPath] = {}
    _logger = logging.getLogger(__name__)

    # 推理参数配置
    CONFIDENCE_THRESHOLD = 0.1
    MAX_REASONING_DEPTH = 10
    REASONING_TIMEOUT = 5.0  # 秒

    @classmethod
    def get_stats(cls) -> ReasoningStats:
        """获取推理统计信息"""
        return cls._stats

    @classmethod
    def reset_stats(cls):
        """重置推理统计信息"""
        cls._stats = ReasoningStats()
        cls._reasoning_paths.clear()

    @classmethod
    def get_reasoning_paths(cls) -> Dict[str, ReasoningPath]:
        """获取推理路径记录"""
        return cls._reasoning_paths.copy()

    @classmethod
    def _create_reasoning_path(cls, rule_type: str) -> ReasoningPath:
        """创建新的推理路径"""
        import uuid
        path_id = f"{rule_type}_{uuid.uuid4().hex[:8]}"
        path = ReasoningPath(path_id=path_id)
        cls._reasoning_paths[path_id] = path
        return path

    @classmethod
    def _validate_premises(cls, term1: Term, term2: Term, sentence: Sentence, belief: Sentence) -> bool:
        """验证推理前提的有效性"""
        if Statement.invalid_statement(term1, term2):
            return False

        if not sentence or not belief:
            return False

        if not sentence.truth or not belief.truth:
            return False

        # 检查置信度阈值
        if (sentence.truth.get_confidence() < cls.CONFIDENCE_THRESHOLD or
            belief.truth.get_confidence() < cls.CONFIDENCE_THRESHOLD):
            return False

        return True

    @classmethod
    def _calculate_dynamic_confidence(cls, base_confidence: float, rule_type: str,
                                    premise_count: int = 2) -> float:
        """动态计算置信度"""
        # 基于推理类型调整置信度
        type_multipliers = {
            'deduction': 0.95,    # 演绎推理置信度最高
            'induction': 0.75,    # 归纳推理置信度中等
            'abduction': 0.60,    # 溯因推理置信度较低
            'analogy': 0.70,      # 类比推理置信度中等
            'comparison': 0.80    # 比较推理置信度较高
        }

        multiplier = type_multipliers.get(rule_type, 0.70)

        # 基于前提数量调整（前提越多，置信度可能降低）
        premise_factor = max(0.5, 1.0 - (premise_count - 2) * 0.1)

        return min(0.99, base_confidence * multiplier * premise_factor)

    @classmethod
    def ded_exe(cls, term1: Term, term2: Term, sentence: Sentence, belief: Sentence, nal: DerivationContext):
        """
        增强版演绎推理与例示推理组合方法
        <pre>
        {<S ==> M>, <M ==> P>} |- {<S ==> P>, <P ==> S>}
        </pre>

        新增特性：
        - 推理路径追踪
        - 动态置信度调整
        - 性能监控
        - 错误处理增强

        参数:
            term1: 第一个新任务的主词
            term2: 第一个新任务的谓词
            sentence: 第一个前提语句
            belief: 第二个前提语句
            nal: 指向内存系统的引用
        """
        start_time = time.time()
        reasoning_path = cls._create_reasoning_path('deduction')

        try:
            # 验证前提有效性
            if not cls._validate_premises(term1, term2, sentence, belief):
                reasoning_path.complete(success=False)
                cls._stats.update_inference('deduction', False, time.time() - start_time, 0.0)
                return

            statement_class = sentence.term.__class__
            if statement_class != belief.term.__class__:
                reasoning_path.complete(success=False)
                cls._stats.update_inference('deduction', False, time.time() - start_time, 0.0)
                return

            # 确定内容类别
            if statement_class == Inheritance.__class__ or statement_class == Similarity.__class__:
                content_class = statement_class
            elif statement_class == Implication.__class__ or statement_class == Equivalence.__class__:
                content_class = statement_class
            else:
                reasoning_path.complete(success=False)
                cls._stats.update_inference('deduction', False, time.time() - start_time, 0.0)
                return

            # 获取时序顺序
            order1 = sentence.term.get_temporal_order() if hasattr(sentence.term, 'get_temporal_order') else 0
            order2 = belief.term.get_temporal_order() if hasattr(belief.term, 'get_temporal_order') else 0
            order = TemporalRules.ded_exe_order(order1, order2) if hasattr(TemporalRules, 'ded_exe_order') else 0

            if hasattr(TemporalRules, 'ORDER_INVALID') and order == TemporalRules.ORDER_INVALID:
                reasoning_path.complete(success=False)
                cls._stats.update_inference('deduction', False, time.time() - start_time, 0.0)
                return

            # 获取真值
            value1 = sentence.truth
            value2 = belief.truth
            truth1 = None
            truth2 = None

            if not (sentence.is_question() or sentence.is_quest()):
                if sentence.is_goal():
                    truth1 = TruthFunctions.desire_weak(value1, value2, nal.nar.narParameters)
                    truth2 = TruthFunctions.desire_weak(value1, value2, nal.nar.narParameters)
                else:
                    # 判断语句 - 使用增强的真值计算
                    truth1 = TruthFunctions.deduction(value1, value2, nal.nar.narParameters)
                    truth2 = TruthFunctions.exemplification(value1, value2, nal.nar.narParameters)

                    # 动态调整置信度
                    if truth1:
                        adjusted_conf1 = cls._calculate_dynamic_confidence(
                            truth1.get_confidence(), 'deduction', 2)
                        truth1 = TruthValue(truth1.get_frequency(), adjusted_conf1,
                                          truth1.is_analytic(), nal.nar.narParameters)

                    if truth2:
                        adjusted_conf2 = cls._calculate_dynamic_confidence(
                            truth2.get_confidence(), 'deduction', 2)
                        truth2 = TruthValue(truth2.get_frequency(), adjusted_conf2,
                                          truth2.is_analytic(), nal.nar.narParameters)

            # 获取预算值
            if sentence.is_question() or sentence.is_quest():
                budget1 = BudgetFunctions.backward(value2, nal)
                budget2 = BudgetFunctions.backward(value2, nal)
            else:
                budget1 = BudgetFunctions.forward(truth1, nal)
                budget2 = BudgetFunctions.forward(truth2, nal)

            # 创建任务
            content1 = Statement.make(content_class, term1, term2, order)
            content2 = Statement.make(content_class, term2, term1,
                                    TemporalRules.reverse_order(order) if hasattr(TemporalRules, 'reverse_order') else order)

            success_count = 0
            if content1 is not None:
                try:
                    nal.double_premise_task(content1, truth1, budget1, False, False)
                    reasoning_path.add_step('deduction', [sentence, belief], content1, truth1)
                    success_count += 1
                except Exception as e:
                    cls._logger.warning(f"Failed to create deduction task 1: {e}")

            if content2 is not None:
                try:
                    nal.double_premise_task(content2, truth2, budget2, False, False)
                    reasoning_path.add_step('exemplification', [sentence, belief], content2, truth2)
                    success_count += 1
                except Exception as e:
                    cls._logger.warning(f"Failed to create deduction task 2: {e}")

            # 完成推理路径
            success = success_count > 0
            reasoning_path.complete(success=success)

            # 更新统计信息
            avg_confidence = 0.0
            if truth1 and truth2:
                avg_confidence = (truth1.get_confidence() + truth2.get_confidence()) / 2
            elif truth1:
                avg_confidence = truth1.get_confidence()
            elif truth2:
                avg_confidence = truth2.get_confidence()

            cls._stats.update_inference('deduction', success, time.time() - start_time, avg_confidence)

        except Exception as e:
            cls._logger.error(f"Error in deduction reasoning: {e}")
            reasoning_path.complete(success=False)
            cls._stats.update_inference('deduction', False, time.time() - start_time, 0.0)
            nal.double_premise_task(content2, truth2, budget2, false, false)

    @staticmethod
    def abd_ind_com(term1: Term, term2: Term, sentence1: Sentence, sentence2: Sentence, figure: int, nal: DerivationContext) -> bool:
        """
        {<M ==> S>, <M ==> P>} |- {<S ==> P>, <P ==> S>, <S <=> P>}
        归纳、演绎和比较推理的组合方法
        根据两个前提语句生成归纳、演绎和比较三种推理结果

        参数:
            term1: 第一个新任务的主词
            term2: 第一个新任务的谓词
            sentence1: 第一个前提语句
            sentence2: 第二个前提语句
            figure: 共享词项在前提中的位置索引
            nal: 指向内存系统的引用

        返回:
            bool: 是否使用了感知词项( sensational terms )
        """
        if Statement.invalid_statement(term1, term2) or Statement.invalid_pair(term1, term2):
            return False

        statement_class = sentence1.term.__class__
        if statement_class != sentence2.term.__class__:
            return False

        if statement_class == Inheritance.__class__ or statement_class == Similarity.__class__:
            content_class = statement_class
        elif statement_class == Implication.__class__ or statement_class == Equivalence.__class__:
            content_class = statement_class
        else:
            return False

        # Get temporal order
        order1 = sentence1.term.get_temporal_order()
        order2 = sentence2.term.get_temporal_order()
        order = TemporalRules.abd_ind_com_order(order1, order2)
        if order == TemporalRules.ORDER_INVALID:
            return False

        # Get the truth values
        value1 = sentence1.truth
        value2 = sentence2.truth
        truth1 = None
        truth2 = None
        truth3 = None

        if sentence1.is_goal():
            truth1 = TruthFunctions.desire_strong(value1, value2, nal.nar.narParameters)  # P --> S
            truth2 = TruthFunctions.desire_weak(value2, value1, nal.nar.narParameters)    # S --> P
            truth3 = TruthFunctions.desire_strong(value1, value2, nal.nar.narParameters)  # S <-> P
        elif sentence1.is_judgment():
            truth1 = TruthFunctions.abduction(value1, value2, nal.nar.narParameters)      # P --> S
            truth2 = TruthFunctions.abduction(value2, value1, nal.nar.narParameters)      # S --> P
            truth3 = TruthFunctions.comparison(value1, value2, nal.nar.narParameters)     # S <-> P

        # Check for sensational terms
        if term1.imagination is not None and term2.imagination is not None:
            # Handle sensational terms
            T = term1.imagination.abduction_or_comparison_to(term2.imagination, True)
            nal.double_premise_task(
                Statement.make(NativeOperator.SIMILARITY, term1, term2, TemporalRules.ORDER_NONE),
                T, BudgetFunctions.forward(T, nal), False, False)

            T2 = term1.imagination.abduction_or_comparison_to(term2.imagination, False)
            nal.double_premise_task(
                Statement.make(NativeOperator.INHERITANCE, term1, term2, TemporalRules.ORDER_NONE),
                T2, BudgetFunctions.forward(T2, nal), False, False)

            T3 = term2.imagination.abduction_or_comparison_to(term1.imagination, False)
            nal.double_premise_task(
                Statement.make(NativeOperator.INHERITANCE, term2, term1, TemporalRules.ORDER_NONE),
                T3, BudgetFunctions.forward(T3, nal), False, False)

            return True

        # Get the budget values
        if sentence1.is_question() or sentence1.is_quest():
            budget1 = BudgetFunctions.backward(value2, nal)
            budget2 = BudgetFunctions.backward(value2, nal)
            budget3 = BudgetFunctions.backward(value2, nal)
        else:
            budget1 = BudgetFunctions.forward(truth1, nal)
            budget2 = BudgetFunctions.forward(truth2, nal)
            budget3 = BudgetFunctions.forward(truth3, nal)

        # Create the tasks
        if order != TemporalRules.ORDER_INVALID:
            occurrence_time1 = sentence1.get_occurrence_time()
            occurrence_time2 = sentence2.get_occurrence_time()

            nal.get_the_new_stamp().set_occurrence_time(occurrence_time1)
            nal.double_premise_task(
                Statement.make(content_class, term1, term2, order),
                truth1, budget1, False, False)

            nal.get_the_new_stamp().set_occurrence_time(occurrence_time2)
            nal.double_premise_task(
                Statement.make(content_class, term2, term1, TemporalRules.reverse_order(order)),
                truth2, budget2, False, False)

            nal.get_the_new_stamp().set_occurrence_time(occurrence_time1)
            nal.double_premise_task(
                Statement.make_sym(content_class, term1, term2, order),
                truth3, budget3, False, False)

        # Handle higher order statements
        if (nal.nar.narParameters.BREAK_NAL_HOL_BOUNDARY and order1 == order2 and
            sentence1.term.is_higher_order_statement() and sentence2.term.is_higher_order_statement()):

            if truth3 is not None:
                truth3 = truth3.clone()

            nal.double_premise_task(
                Statement.make(NativeOperator.SIMILARITY, term1, term2, TemporalRules.ORDER_NONE),
                truth3, budget3.clone(), False, False)

        return False

    @staticmethod
    def resemblance(term1: Term, term2: Term, belief: Sentence, sentence: Sentence, figure: int, nal: DerivationContext):
        """
        {<S <=> M>, <M <=> P>} |- <S <=> P>
        相似性推理方法
        根据两个相似性前提推导出新的相似性结论

        参数:
            term1: 新任务的主词
            term2: 新任务的谓词
            belief: 第一个前提语句
            sentence: 第二个前提语句
            figure: 共享词项在前提中的位置索引
            nal: 指向内存系统的引用
        """
        if Statement.invalid_statement(term1, term2):
            return

        order1 = belief.term.get_temporal_order()
        order2 = sentence.term.get_temporal_order()
        order = TemporalRules.resemblance_order(order1, order2, figure)

        if order == TemporalRules.ORDER_INVALID:
            return

        st = belief.term
        truth = None

        if not (sentence.is_question() or sentence.is_quest()):
            if sentence.is_goal():
                truth = TruthFunctions.desire_strong(sentence.truth, belief.truth, nal.nar.narParameters)
            else:
                truth = TruthFunctions.resemblance(belief.truth, sentence.truth, nal.nar.narParameters)

        if sentence.is_question() or sentence.is_quest():
            budget = BudgetFunctions.backward(belief.truth, nal)
        else:
            budget = BudgetFunctions.forward(truth, nal)

        content = Statement.make_sym(sentence.term.__class__, term1, term2, order)

        if content is not None:
            nal.double_premise_task(content, truth, budget, False, False)

        # Handle higher order statements
        if (nal.nar.narParameters.BREAK_NAL_HOL_BOUNDARY and not sentence.term.has_var_indep() and
            isinstance(st, Equivalence) and order1 == order2 and
            belief.term.is_higher_order_statement() and sentence.term.is_higher_order_statement()):

            value1 = sentence.truth
            value2 = belief.truth

            if value1 is not None and value2 is not None:
                truth3 = TruthFunctions.comparison(value1, value2, nal.nar.narParameters)
                budget3 = BudgetFunctions.forward(truth3, nal)

                nal.double_premise_task(
                    Statement.make(NativeOperator.SIMILARITY, term1, term2, TemporalRules.ORDER_NONE),
                    truth3, budget3.clone(), False, False)

    @staticmethod
    def analogy(subj: Term, pred: Term, asym: Sentence, sym: Sentence, figure: int, nal: DerivationContext):
        """
        {<S ==> P>, <M <=> P>} |- <S ==> P>
        类比推理方法
        结合一个非对称前提和一个对称前提进行类比推理

        参数:
            subj: 新任务的主词
            pred: 新任务的谓词
            asym: 非对称前提语句
            sym: 对称前提语句
            figure: 共享词项在前提中的位置索引
            nal: 指向内存系统的引用
        """
        if Statement.invalid_statement(subj, pred):
            return

        order1 = asym.term.get_temporal_order()
        order2 = sym.term.get_temporal_order()
        order = TemporalRules.analogy_order(order1, order2, figure)

        if order == TemporalRules.ORDER_INVALID:
            return

        st = asym.term
        truth = None
        budget = None
        sentence = nal.get_current_task().sentence
        task_term = sentence.term

        if sentence.is_question() or sentence.is_quest():
            if task_term.is_commutative():
                if asym.truth is None:  # a question for example
                    return
                budget = BudgetFunctions.backward_weak(asym.truth, nal)
            else:
                if sym.truth is None:  # a question for example
                    return
                budget = BudgetFunctions.backward(sym.truth, nal)
        else:
            if sentence.is_goal():
                truth = TruthFunctions.lookup_truth_function_by_bool_and_compute(
                    task_term.is_commutative(),
                    TruthFunctions.EnumType.DESIRE_WEAK,
                    TruthFunctions.EnumType.DESIRE_STRONG,
                    asym.truth, sym.truth, nal.nar.narParameters
                )
            else:
                truth = TruthFunctions.analogy(asym.truth, sym.truth, nal.nar.narParameters)

            budget = BudgetFunctions.forward(truth, nal)

        nal.double_premise_task(Statement.make(st.__class__, subj, pred, order), truth, budget, False, False)

    @staticmethod
    def conditional_deduction(cond: Term, statement: Statement, task: Sentence, belief: Sentence, nal: DerivationContext):
        """
        {<(&&, S1, S2) ==> P>, <S1 ==> S3>} |- <(&&, S3, S2) ==> P>

        Args:
            cond: The condition of the implication
            statement: The premise
            task: The task
            belief: The belief
            nal: Reference to the memory
        """
        if not isinstance(cond, Conjunction):
            return False

        task_sentence = task.sentence
        task_term = task_sentence.term
        belief_term = belief.term

        if not (isinstance(task_term, Implication) and isinstance(belief_term, Inheritance)):
            return False

        # Get the conjunction components
        conj = cond
        component_common = None
        component_replace = None

        # Find the common component and the one to be replaced
        for i in range(conj.size()):
            component = conj.term[i]
            if component.equals(belief_term.get_subject()):
                component_common = component
                component_replace = belief_term.get_predicate()
                break

        if component_common is None:
            return False

        # Create a new conjunction with the replaced component
        conj_components = []
        for i in range(conj.size()):
            component = conj.term[i]
            if component.equals(component_common):
                conj_components.append(component_replace)
            else:
                conj_components.append(component)

        new_cond = Conjunction.make(conj_components, conj.get_temporal_order())

        # Create the new statement
        content = Statement.make(task_term.__class__, new_cond, task_term.get_predicate(), task_term.get_temporal_order())

        if content is None:
            return False

        truth1 = task_sentence.truth
        truth2 = belief.truth
        truth = None
        budget = None

        if not (task_sentence.is_question() or task_sentence.is_quest()):
            if task_sentence.is_goal():
                truth = TruthFunctions.desire_deduction(truth1, truth2, nal.nar.narParameters)
            else:  # isJudgment
                truth = TruthFunctions.deduction(truth1, truth2, nal.nar.narParameters)

        if task_sentence.is_question() or task_sentence.is_quest():
            budget = BudgetFunctions.backward_weak(truth2, nal)
        else:
            budget = BudgetFunctions.forward(truth, nal)

        nal.double_premise_task(content, truth, budget, False, task_sentence.is_judgment())
        return True

    @staticmethod
    def conditional_abduction(cond1: Term, cond2: Term, st1: Statement, st2: Statement, nal: DerivationContext) -> bool:
        """
        {<(&&, S2, S3) ==> P>, <(&&, S1, S3) ==> P>} |- <S1 ==> S2>

        Args:
            cond1: The condition of the first premise
            cond2: The condition of the second premise
            st1: The first premise
            st2: The second premise
            nal: Reference to the memory

        Returns:
            bool: Whether there are derived tasks
        """
        if not (isinstance(st1, Implication) and isinstance(st2, Implication)):
            return False

        if not (isinstance(cond1, Conjunction) or isinstance(cond2, Conjunction)):
            return False

        # Get the predicates
        pred1 = st1.get_predicate()
        pred2 = st2.get_predicate()

        # Check if the predicates are equal
        if not pred1.equals(pred2):
            return False

        # Get the components of the conjunctions
        conj1 = cond1
        conj2 = cond2

        # Find the common component and the ones to be compared
        component_common = None
        component1 = None
        component2 = None

        for i in range(conj1.size()):
            for j in range(conj2.size()):
                if conj1.term[i].equals(conj2.term[j]):
                    component_common = conj1.term[i]
                    break
            if component_common is not None:
                break

        if component_common is None:
            return False

        # Find the components to be compared
        for i in range(conj1.size()):
            if not conj1.term[i].equals(component_common):
                component1 = conj1.term[i]
                break

        for i in range(conj2.size()):
            if not conj2.term[i].equals(component_common):
                component2 = conj2.term[i]
                break

        if component1 is None or component2 is None:
            return False

        sentence = nal.get_current_task().sentence
        value1 = sentence.truth
        value2 = nal.get_current_belief().truth

        # Process in both directions
        for loop in range(2):
            is_first_loop = loop == 0
            term1_in_loop = component1 if is_first_loop else component2
            term2_in_loop = component2 if is_first_loop else component1

            if term1_in_loop is None:
                continue

            if term2_in_loop is not None:
                content = Statement.make(
                    st2.__class__ if is_first_loop else st1.__class__,
                    term2_in_loop, term1_in_loop,
                    st2.get_temporal_order() if is_first_loop else st1.get_temporal_order()
                )
            else:
                content = term1_in_loop
                if content.has_var_indep():
                    return False

            truth = None
            budget = None

            if sentence.is_question() or sentence.is_quest():
                budget = BudgetFunctions.backward_weak(value2, nal)
            else:
                if sentence.is_goal():
                    truth = TruthFunctions.lookup_truth_function_by_bool_and_compute(
                        True,  # keepOrder
                        TruthFunctions.EnumType.DESIRE_DED,
                        TruthFunctions.EnumType.DESIRE_IND,
                        value1, value2, nal.nar.narParameters
                    )
                else:  # isJudgment
                    if is_first_loop:
                        truth = TruthFunctions.abduction(value2, value1, nal.nar.narParameters)
                    else:
                        truth = TruthFunctions.abduction(value1, value2, nal.nar.narParameters)

                budget = BudgetFunctions.forward(truth, nal)

            nal.double_premise_task(content, truth, budget, False, False)

        return True

    @staticmethod
    def conditional_analogy(cond: Term, statement1: Statement, statement2: Statement, side: int, nal: DerivationContext):
        """
        {<(&&, S1, S2) <=> P>, <S1 ==> S3>} |- <(&&, S3, S2) ==> P>

        Args:
            cond: The condition of the implication
            statement1: The equivalence premise
            statement2: The inheritance premise
            side: The location of the shared term
            nal: Reference to the memory
        """
        if not isinstance(cond, Conjunction):
            return False

        task_sentence = nal.get_current_task().sentence
        task_term = task_sentence.term
        belief_term = nal.get_current_belief().term

        # Get the conjunction components
        conj = cond
        component_common = None
        component_replace = None

        # Find the common component and the one to be replaced
        for i in range(conj.size()):
            component = conj.term[i]
            if component.equals(belief_term.get_subject()):
                component_common = component
                component_replace = belief_term.get_predicate()
                break

        if component_common is None:
            return False

        # Create a new conjunction with the replaced component
        conj_components = []
        for i in range(conj.size()):
            component = conj.term[i]
            if component.equals(component_common):
                conj_components.append(component_replace)
            else:
                conj_components.append(component)

        new_cond = Conjunction.make(conj_components, conj.get_temporal_order())

        # Create the new statement
        content = Statement.make(task_term.__class__, new_cond, task_term.get_predicate(), task_term.get_temporal_order())

        if content is None:
            return False

        truth1 = task_sentence.truth
        truth2 = nal.get_current_belief().truth
        truth = None
        budget = None

        if not (task_sentence.is_question() or task_sentence.is_quest()):
            if task_sentence.is_goal():
                if isinstance(statement1, Equivalence):
                    truth = TruthFunctions.desire_strong(truth1, truth2, nal.nar.narParameters)
                elif side == 0:
                    truth = TruthFunctions.desire_ind(truth1, truth2, nal.nar.narParameters)
                else:
                    truth = TruthFunctions.desire_ded(truth1, truth2, nal.nar.narParameters)
            else:  # isJudgment
                if isinstance(statement1, Equivalence):
                    truth = TruthFunctions.analogy(truth2, truth1, nal.nar.narParameters)
                elif side == 0:
                    truth = TruthFunctions.deduction(truth1, truth2, nal.nar.narParameters)
                else:
                    truth = TruthFunctions.abduction(truth2, truth1, nal.nar.narParameters)

        if task_sentence.is_question() or task_sentence.is_quest():
            budget = BudgetFunctions.backward_weak(truth2, nal)
        else:
            budget = BudgetFunctions.forward(truth, nal)

        nal.double_premise_task(content, truth, budget, False, task_sentence.is_judgment())
        return True

    @staticmethod
    def reversion(compound1: Term, compound2: Term, statement1: Statement, statement2: Statement, nal: DerivationContext):
        """
        {<(&&, S1, S2) ==> P1>, <(&&, S1, S2) ==> P2>} |- <P1 <=> P2>

        Args:
            compound1: The compound term in the first premise
            compound2: The compound term in the second premise
            statement1: The first premise
            statement2: The second premise
            nal: Reference to the memory
        """
        if not (compound1.equals(compound2)):
            return False

        task = nal.get_current_task()
        sentence = task.sentence
        belief = nal.get_current_belief()

        content = Statement.make_sym(
            sentence.term.__class__,
            statement1.get_predicate(),
            statement2.get_predicate(),
            statement1.get_temporal_order()
        )

        if content is None:
            return False

        truth1 = sentence.truth
        truth2 = belief.truth
        truth = None
        budget = None

        compound_task = task.get_term().equals(statement1)

        if not (sentence.is_question() or sentence.is_quest()):
            if sentence.is_goal():
                truth = TruthFunctions.lookup_truth_function_by_bool_and_compute(
                    compound_task,
                    TruthFunctions.EnumType.DESIRE_WEAK,
                    TruthFunctions.EnumType.DESIRE_DED,
                    truth1, truth2, nal.nar.narParameters
                )
            else:
                truth = TruthFunctions.lookup_truth_function_by_bool_and_compute(
                    compound_task,
                    TruthFunctions.EnumType.COMPARISON,
                    TruthFunctions.EnumType.ANALOGY,
                    truth1, truth2, nal.nar.narParameters
                )

        if sentence.is_question() or sentence.is_quest():
            budget = BudgetFunctions.backward_weak(truth2, nal)
        else:
            budget = BudgetFunctions.forward(truth, nal)

        nal.double_premise_task(content, truth, budget, False, sentence.is_judgment() and not compound_task)
        return True
