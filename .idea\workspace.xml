<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5364511b-dfb5-48bf-8534-d1f847bf722f" name="更改" comment="大改-首次提交">
      <change afterPath="$PROJECT_DIR$/.comate/mcp.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/codeStyles/Project.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/codeStyles/codeStyleConfig.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/Project_Default.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.vscode/settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/LINARS_Python版本优化完善总结.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/config/visbles.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/LidaFactories.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/LidaXMLSchema.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/agent.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/alifeAgent.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/factoryData.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/guiCommands.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/guiPanels.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/icons.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/lidaConfig.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/logging.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/objects.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/configs/operations.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars-all-by-ag.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars.egg-info/PKG-INFO" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars.egg-info/SOURCES.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars.egg-info/dependency_links.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars.egg-info/requires.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars.egg-info/top_level.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/automenta/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/automenta/vivisect/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/automenta/vivisect/dimensionalize/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/automenta/vivisect/dimensionalize/hyperassociative_map.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/automenta/vivisect/vis.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/benchmark/performance_test.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/benchmark/test_data_generator.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/__main__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/app.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/config/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/config/web_app_config.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/base_controller.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/file_controller.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/im_control_detector.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/kg_manager_controller.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/nlp_controller.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/question_controller.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/xr0_controller.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/controllers/xr_controller.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/dal/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/dal/impl/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/dal/impl/kg_repository_impl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/dal/kg_repository.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/entity/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/entity/qa_entity_item.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/query/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/query/graph_query.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/service/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/service/impl/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/service/impl/kg_graph_service_impl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/service/kg_graph_service.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/blog/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/blog/base.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/blog/index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/blog/m.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/element-style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/fonts/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/fonts/element-icons.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/fonts/element-icons.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/index0.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/jquery.jsonview.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/manager.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/css/x-index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/gzbg.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/line.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/logo/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/logo/login_bg.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/logo/logo-0.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/logo/logo-3.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/logo/logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/logo/logo_o.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/me.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/images/tan_weixin_qr_1.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/index999.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/fly/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/fly/fly.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/fly/layim.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/3.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/4.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/5.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/6.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/7.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/demo/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/desc.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/logo-2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/layui/logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/other/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/other/upyun.png-t=1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/sentsin/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/images/sentsin/night.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/build/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/build/images/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/build/images/face/2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/build/images/face/38.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/build/images/face/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/release/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/release/laydate/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/release/laydate/dist/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/release/laydate/dist/laydate.js-v=201711160800" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/contextMenu.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/global.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/independents.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/layui.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/layui.demo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/layui0.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/menu.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/modules/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/modules/code.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/modules/layer/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/modules/layer/default/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/modules/layer/default/layer.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/modules/layim/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/css/modules/layim/layim.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/font/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/font/iconfont.eot-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/font/iconfont.svg-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/font/iconfont.ttf-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/font/iconfont.woff-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/images/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/images/face/2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/images/face/20.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/images/face/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/carousel.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/code.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/element.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/flow.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/form.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/getList1.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/jquery.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/laydate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/layedit.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/layer.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/layim-原版.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/layim.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/laypage.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/laytpl.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layer-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layim-mobile-open.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layim-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/upload-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/zepto.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/table.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/tree.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/upload.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/lay/modules/util.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/layui/src/layui.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/demo.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/face.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/global/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/global/global.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/jie.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/req.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/socket.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/socket000.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/strophe-1.2.8.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/strophe-1.2.8.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/user.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/webim.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/res.layui.com/mods/websdk.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/chatC.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/chat_win.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/chatlog.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/createGroup.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/createGroup0.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/demo/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/demo/index.htm" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/demo/laydate.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/demo/layer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/demo/laypage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/demo/laytpl.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/find.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/getInformation.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/layim/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/layim/json/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/layim/json/getList1.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/layim/json/layim.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/chatC/www.layui.com/msgbox.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/d3.v4.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/d3.v4.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/directives.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/error-handler.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/html2canvas.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/iconfont.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/jquery.jsonview.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/jquery.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/kgbuilder.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/lodash.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/sidebarAdmin.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/js/vue.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/react_build/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/react_build/layout.worker.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/codemirror-cypher.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/codemirror.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/cy2neod3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/cypher.datatable.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/data.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/jquery.dataTables.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/neo4d3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/neod3-visualization.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/neod3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/sweet-alert.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/scripts/vendor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/Animation.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/Common.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/IconStylesheet.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/NotoSans-Regular.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/NotoSansArabic-Regular.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/OrbitControls.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/Roboto-msdf.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/Roboto-msdf.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/Stats.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/TrackballControls.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/analytics.kineviz.countly.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/app.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/app.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/app.nodeModules.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/app.reactdatagrid.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/bootstrap.bundle.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/bootstrap.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/catchreport.kineviz.com.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/crossfilter.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/cypher-editor-support.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/d3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/dc.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/dc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/default.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/font-awesome.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/fontawesome-webfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/graphxr-icons.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/graphxr-schema(1).json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/graphxr-schema.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/gui.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/hopscotch.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/hopscotch.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/html2canvas.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/iconfont.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/iconfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/icons.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/icons.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/jquery-ui.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/jquery-ui.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/jquery.fullscreen-min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/jquery.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/jszip.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/loader.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/loading.mp4" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/mapbox-gl.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/mapbox-gl.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/mathbox.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/mathbox.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/md5.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/pin.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/socket.io.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/three.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/threex.keyboardstate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/vendor.bundle.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/static/visual-query-mapping.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/codemirror-neo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/codemirror.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/cy2neo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/datatable.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/fonts/FontAwesome.otf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/fonts/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/fonts/fontawesome-webfont.eot" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/fonts/fontawesome-webfont.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/fonts/fontawesome-webfont.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/fonts/fontawesome-webfont.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/gh-fork-ribbon.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/images/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/images/maze-black.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/neod3.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/sweet-alert.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/styles/vendor.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/fonts/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/fonts/w-e-icon.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/wangEditor.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/wangEditor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/wangEditor.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/wangEditor.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/static/wangeditor/wangEditor.min.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/kg/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/kg/home.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/kg/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/kg/popse.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/focus.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/footer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/header.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/headerAdmin.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/layout.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/layout3.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/templates/share/layoutAdmin.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/aes_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/csv_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/date_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/excel_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/graph_page_record.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/neo4j_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/com/warmer/kgmaker/utils/response.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/alife/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/alife/elements/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/alife/elements/alife_object.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/Action.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/ActionImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/ActionSelection.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/ActionSelectionBackgroundTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/ActionSelectionListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/BasicActionSelection.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/Behavior.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/BehaviorImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/PreafferenceListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ActionSelection/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/ALifeEnvironment.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/EnvironmentBackgroundTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/elements/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/elements/agent_object.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/elements/predator_object.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/attack_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/eat_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/flee_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/get_food_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/move_agent_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/see_objects_in_cell.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/turn_around_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/turn_left_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operations/turn_right_operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operators/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operators/around.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operators/eat.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operators/flee.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operators/left.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operators/move.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Environment/operators/right.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Featuredetectors/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Featuredetectors/health_detector.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Featuredetectors/listen_detector.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Featuredetectors/object_detector.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Featuredetectors/vision_detector.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Modules/BasicSensoryMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Modules/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/config/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/config/visbles.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/LidaFactories.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/LidaXMLSchema.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/agent.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/alifeAgent.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/factoryData.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/guiCommands.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/guiPanels.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/icons.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/lidaConfig.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/logging.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/objects.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/configs/operations.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/guipanels/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/run.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/run0.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/AttentionCodelets/AttentionCodelet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/AttentionCodelets/AttentionCodeletImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/AttentionCodelets/AttentionCodeletModule.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/AttentionCodelets/DefaultAttentionCodelet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/AttentionCodelets/NeighborhoodAttentionCodelet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/AttentionCodelets/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/EnhancedNeoUtil.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/term_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Environment/Environment.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Environment/EnvironmentImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Environment/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/EpisodicMemory/CueListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/EpisodicMemory/EpisodicMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/EpisodicMemory/EpisodicMemoryImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/EpisodicMemory/LocalAssociationListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/EpisodicMemory/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/FrameworkModule.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/FrameworkModuleImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/FullyInitializable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/Initializable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/InitializableImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_factory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_starter.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_xml_factory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/config_utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/factories_data_xml_loader.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/framework_formatter.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/framework_gui_def.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/framework_gui_panel_def.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/framework_task_def.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/global_initializer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/initializer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/linkable_def.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/module_usage.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/parameters.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/strategy_def.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/xml_utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/ModuleListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/ModuleName.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Activation/Activatible.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Activation/ActivatibleImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Activation/Learnable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Activation/LearnableImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Activation/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/ElementFactory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/ExtendedId.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Link.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkCategory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkCategoryImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Linkable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkableImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/Node.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructure.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/RefractoryPeriod.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/UnmodifiableNodeStructureImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Strategies/DecayStrategy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Strategies/DefaultDecayStrategy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Strategies/ExciteStrategy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Strategies/LinearExciteStrategy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Strategies/Strategy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Strategies/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/Codelet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/CodeletImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/FrameworkTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/FrameworkTaskImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/TaskManager.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/TaskSpawner.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/TaskSpawnerImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/agent.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/agent_impl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/BroadcastListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Coalition.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/CoalitionImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/GlobalWorkspace.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/GlobalWorkspaceImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/GlobalWorkspaceInitializer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/RefractoryPeriod.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/StartTriggersTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Tasks/StartTriggersTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Tasks/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/AggregateCoalitionActivationTrigger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/BroadcastTrigger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/IndividualCoalitionActivationTrigger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/NoBroadcastOccurringTrigger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/NoCoalitionArrivingTrigger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/TriggerListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/TriggerTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/Triggers/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/GlobalWorkspace/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Integration/IntegrationTest.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Integration/NarsLidaBridge.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Module.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Module/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Module/initialization/DefaultLogger.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Module/initialization/ModuleInterface.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Module/initialization/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/ChartTreeSet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/ComprehensiveTestFramework.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/LanGen.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/LanGenImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/LanGenInitializer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/LanListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/NarsSchemaIntegration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/PROJECT_SUMMARY.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/ParameterManager.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SchemaExecutor.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SchemaFactory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SchemaSearchIntegration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SemanticAnalyzTask0.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SubGraph.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SubGraphSet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/TreeBag.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/TreeChart.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/TreeNode.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/Tree_nest.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/VariableBindingSystem.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/run_comprehensive_tests.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/test_nars_schema_integration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/test_schema_execution.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/test_schema_search_integration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/test_variable_binding.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/BasicPamInitializer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PAMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PAMemoryImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamLink.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamLinkImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamLinkable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamNode.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamNodeImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PropagationStrategy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/AddLinkToPerceptTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/AddNodeStructureToPerceptTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/AddNodeToPerceptTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/BasicDetectionAlgorithm.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/CreateActRootTask0.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/CreateSimpleSceneTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/CreateSuccTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/DetectionAlgorithm.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/DoMindActTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/DoSelectSeqTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/DoSelectTreeTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/DoSimpleSceneTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/DoSuccTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ExcitationTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ForEachTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GrammarTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/IsaPamTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/LearnTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/MultipleDetectionAlgorithm.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/NarCycleTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/OptimizedControlFlowTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/OptimizedDoSelectTreeTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/OptimizedDoSuccTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/OptimizedForEachTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGTreeTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/PropagationTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ReasonTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/SearchSB.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/SelectConceptTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/VarManagerTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/test_optimized_control_flow.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/UpscalePropagationStrategy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/BasicProceduralMemoryInitializer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/Condition.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/ConditionImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/NodeCondition.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/ProceduralMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/ProceduralMemoryImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/ProceduralMemoryListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/ProceduralUnit.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/Scheme.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/SchemeImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/ProceduralMemory/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMemory/BasicSensoryMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMemory/SensoryMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMemory/SensoryMemoryBackgroundTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMemory/SensoryMemoryImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMemory/SensoryMemoryListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMemory/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMotorMemory/BasicSensoryMotorMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMotorMemory/BasicSensoryMotorMemoryInitializer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMotorMemory/SensoryMotorMemory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMotorMemory/SensoryMotorMemoryListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/SensoryMotorMemory/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/StructureBuildingCodelets/BasicStructureBuildingCodelet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/StructureBuildingCodelets/StructureBuildingCodelet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/StructureBuildingCodelets/StructureBuildingCodeletImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/StructureBuildingCodelets/StructureBuildingCodeletModule.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/StructureBuildingCodelets/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/UpdateCsmBackgroundTask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/Workspace.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/BroadcastQueue.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/BroadcastQueueImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WMBufferImpl_mem.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WSBufferImpl_graph.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WorkspaceBuffer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WorkspaceBufferImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceContent.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceListener.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/abstract_term.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/concept.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/term.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/term_node_impl.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/concept/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/concept/process_anticipation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/concept/process_question.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/control/inference_control.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/budget_value.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/item.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/sentence.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/stamp.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/t_link.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/task.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/task_link.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/term_link.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/entity/truth_value.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/budget_functions.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/compositional_rules.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/local_rules.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/local_rules_helper.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/local_rules_stub.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/rule_tables.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/structural_rules.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/syllogistic_rules.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/temporal_rules.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/temporal_rules_stub.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/truth_functions.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/inference/utility_functions.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/interfaces/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/interfaces/eventable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/interfaces/multistepable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/interfaces/pluggable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/interfaces/pub/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/interfaces/pub/reasoner.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/interfaces/timable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/channel.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/events/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/events/answer_handler.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/events/event_emitter.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/events/event_handler.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/events/events.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/events/output_handler.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/events/text_output_handler.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/narsese.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/parser.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/io/symbols.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/conjunction.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/difference_ext.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/difference_int.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/disjunction.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/equivalence.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/image.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/image_ext.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/image_int.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/implication.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/inheritance.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/instance.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/instance_property.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/intersection_ext.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/intersection_int.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/interval.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/negation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/product.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/property.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/set_ext.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/set_int.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/set_tensional.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/similarity.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/statement.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/tense.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/terms.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/variable.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/language/variables.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/main/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/main/nar.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/main/parameters.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/function_operator.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/mental/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/mental/consider.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/mental/doubt.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/mental/evaluate.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/mental/remind.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/mental/wonder.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/add.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/add0.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/attent.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/count.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/cut_str.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/reflect.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/say.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/search_mom.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/search_mos.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/search_sb.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/search_som.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/search_sos.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/misc/system_nar.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/null_operator.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/operation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/operator/operator.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/plugin/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/plugin/mental/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/plugin/mental/emotions.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/plugin/mental/internal_experience.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/plugin/perception/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/plugin/perception/sensory_channel.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/plugin/plugin.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/storage/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/storage/bag1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/storage/buffer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars/org/opennars/storage/internal_experience_buffer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/LidaFactories.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/LidaXMLSchema.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/alifeAgent.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/factoryData.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/guiCommands.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/guiPanels.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/icons.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/lidaConfig.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/logging.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/objects.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/configs/operations.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/food.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/monkey.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/personE.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/personN.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/personS.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/personW.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/rock.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/imgs/tree.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/neo-lida.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/pom0.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/KgmakerApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/TestSome.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/config/WebAppConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/BaseController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/FileController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/ImControlDetector.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/KGManagerController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/NLPController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/QuestionController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/XR0Controller.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/controller/XRController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/dal/IKGraphRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/dal/IKnowledgegraphRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/dal/impl/KGraphRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/entity/QAEntityItem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/query/GraphQuery.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/service/IKGGraphService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/service/IKnowledgegraphService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/service/IQuestionService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/service/impl/KGGraphService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/service/impl/KnowledgegraphService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/AESUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/CSVUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/DateUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/ExcelUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/FileResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/FileResult.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/GraphPageRecord.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/ImageUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/Neo4jUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/QiniuUploadService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/QiniuUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/R.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/SpringContextUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/StringUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/TestUtility.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/TextProcessUtility.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/UploadUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/com/warmer/kgmaker/util/UuidUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/elements/ALifeObject.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/elements/ALifeObjectImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/elements/Cell.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/elements/CellImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/elements/ObjectContainer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/elements/properties/Attributable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/elements/properties/AttributableImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/example/AnimateObject.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/example/WorldFrame.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/gui/ALifePanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/gui/ALifeWorldRenderer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/gui/AttrListModel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/gui/AttributesTableModel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/gui/CellImagePanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/gui/DefaultWorldRenderer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/gui/ImagePanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/opreations/MoveOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/opreations/UpdateStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/opreations/Updateable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/opreations/WorldOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/utils/ConcurrentHashSet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/world/ALifeWorld.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/world/ALifeWorldImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/alife/world/WorldLoader.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/Action.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/ActionImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/ActionSelection.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/ActionSelectionListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/BasicActionSelection.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/Behavior.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/BehaviorImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/PreafferenceListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/actionselection/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/Run.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/Run0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/ALifeEnvironment.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/elements/AgentObject.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/elements/PredatorObject.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/AttackOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/EatOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/FleeOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/GetFoodOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/MoveAgentOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/SeeObjectsInCell.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/TurnAroundOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/TurnLeftOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operations/TurnRightOperation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Around.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Eat.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Flee.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Left.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Move.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/environment/operators/Right.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/featuredetectors/HealthDetector.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/featuredetectors/ListenDetector.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/featuredetectors/ObjectDetector.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/featuredetectors/VisionDetector.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/guipanels/ALifeGuiPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/guipanels/ALifeGuiPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/alifeagent/modules/BasicSensoryMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/AttentionCodelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/AttentionCodeletImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/AttentionCodeletModule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/AttentionTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/BasicAttentionCodelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/DefaultAttentionCodelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/NeighborhoodAttentionCodelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/attentioncodelets/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/data/NeoUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/data/TermUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/environment/ConditioningEnvironment.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/environment/Environment.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/environment/EnvironmentImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/environment/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/episodicmemory/CueListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/episodicmemory/EpisodicMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/episodicmemory/LocalAssociationListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/episodicmemory/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/Agent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/AgentImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/CodeletManagerModule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/FrameworkModule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/FrameworkModuleImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/ModuleListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/ModuleName.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/FrameworkGui1.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/FrameworkGui1.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/FrameworkGuiController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/FrameworkGuiControllerImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/FrameworkGuiFactory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/AddPanelCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/AddTicksCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/Command.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/CommandImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/EnableTicksModeCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/PauseRunningThreadsCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/QuitAllCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/ResetEnvironmentCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/ResumeRunningThreadsCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/SetTimeScaleCommand.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/commands/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/events/FrameworkGuiEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/events/FrameworkGuiEventListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/events/GuiEventProvider.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/events/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ActionSelectionPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ActionSelectionPanel0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ActionSelectionPanel0.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ActionSelectionPanel1.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ActivationChartPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ActivationChartPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/AddEditPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/AddEditPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/BroadcastQueuePanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ChatPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ChatPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ConfigurationFilesPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ConfigurationFilesPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ControlToolBarPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ControlToolBarPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/FrameworkTaskPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/FrameworkTaskPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/GlobalWorkspaceTablePanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/GlobalWorkspaceTablePanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/GuiPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/GuiPanelImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/GuiPanelImpl.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/JIMHistoryTextPane.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/JIMSendTextPane.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/LoggingPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/LoggingPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/Message.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/NodeStructurePanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/NodeStructurePanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/NodeStructureTable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/NodeStructureTable.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ProceduralMemoryPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/ProceduralMemoryPanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/TaskQueuePanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/TaskQueuePanel.jfd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/panels/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/utils/GuiLink.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/utils/GuiUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/utils/NodeIcon.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/utils/NodeStructureGuiAdapter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/gui/utils/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/AgentFactory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/AgentStarter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/AgentXmlFactory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/ConfigUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/FactoriesDataXmlLoader.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/FrameworkFormatter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/FrameworkGuiDef.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/FrameworkGuiPanelDef.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/FrameworkTaskDef.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/FullyInitializable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/GlobalInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/Initializable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/InitializableImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/Initializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/LinkableDef.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/ModuleUsage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/Parameters.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/StrategyDef.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/XmlUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/config/LidaFactories.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/config/LidaGuiDef.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/config/LidaXMLSchema.xsd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/initialization/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/AttentBuffer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/AttentNS.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/ConcurrentHashSet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/ElementFactory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/ExtendedId.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/Link.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/LinkCategory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/LinkImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/Linkable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/Node.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/NodeImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/NodeStructure.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/NodeStructureImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/NodeType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/RefractoryPeriod.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/RootableNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/RootableNodeImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/Translatable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/UnmodifiableNodeStructureImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/activation/Activatible.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/activation/ActivatibleImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/activation/Desirable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/activation/Learnable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/activation/LearnableImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/activation/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/CompoundTerm0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/Entity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/Events.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/MethodStep.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/Objects.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SAction.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SAttrs.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SContext.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SEmotion.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SIntent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SMethod.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SNLU.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SOrder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SPerson.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SSite.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SWorldView.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/SceneStructure.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/shared/scene/WorldStructure.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/DecayStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/DefaultTotalActivationStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/ExciteStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/LinearDecayStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/LinearExciteStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/LinearIncentiveSalienceDecay.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/NoDecayStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/NoExciteStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/SigmoidDecayStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/SigmoidExciteStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/Strategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/StrategyImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/TotalActivationStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/WeightedTotalActivationStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/strategies/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/Codelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/CodeletImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/FrameworkTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/FrameworkTaskImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/RandomizingTaskSpawner.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/TaskManager.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/TaskSpawner.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/TaskSpawnerImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/TaskStatus.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/framework/tasks/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/BroadcastContent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/BroadcastListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/Coalition.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/CoalitionImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/GlobalWorkspace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/GlobalWorkspaceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/GlobalWorkspaceInitalizer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/GlobalWorkspaceInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/AggregateCoalitionActivationTrigger.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/BroadcastTrigger.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/IndividualCoaltionActivationTrigger.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/NoBroadcastOccurringTrigger.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/NoCoalitionArrivingTrigger.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/TriggerListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/TriggerTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/globalworkspace/triggers/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/pam/FeelingPamLinkImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/pam/FeelingPamNodeImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/pam/IncentiveSaliencePropagationTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/pam/MotivationPAMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/pam/MotivationPamInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/proceduralmemory/MotivationProceduralMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/proceduralmemory/MotivationProceduralMemoryInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/shared/FeelingLinkImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/shared/FeelingNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/shared/FeelingNodeImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/shared/Valenceable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/shared/ValenceableImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/workspace/FeelingStructureBuildingCodelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/workspace/MotivationBroadcastQueueImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/workspace/MotivationWorkspace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/motivation/workspace/MotivationWorkspaceInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/ChartTreeSet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarAnalyzTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/GrammarTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/LanGen.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/LanGenImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/LanGenInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/LanListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/LangGSMergeTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/SemanticAnalyzTask0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/ShortestPathFinder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/SubGraph.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/SubGraphSet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/SubGraphSet0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/TreeBag.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/TreeChart.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/TreeNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/Tree_nest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/nlanguage/VisionAnalyzTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/BasicPamInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PAMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PAMemoryImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PamImpl0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PamLink.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PamLinkImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PamLinkable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PamListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PamNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PamNodeImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/PropagationStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/UpscalePropagationStrategy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/AddLinkToPerceptTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/AddNodeStructureToPerceptTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/AddNodeToPerceptTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/BasicDetectionAlgorithm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateActRootTask0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateSimpleSceneTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/CreateSuccTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DetectionAlgorithm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoMindActTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectSeqTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSelectTreeTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSimpleSceneTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/DoSuccTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ExcitationTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ForEachTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalBackgroundTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/GoalTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/IsaPamTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/LearnTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/MultipleDetectionAlgorithm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/NarCycleTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ProcessGBufferTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ProcessGTreeTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/PropagationTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/ReasonTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/SelectConceptTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/VarManagerTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/pam/tasks/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/BasicProceduralMemoryInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/Condition.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralMemoryListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/ProceduralUnit.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/Scheme.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/SchemeImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/proceduralmemory/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymemory/SensoryMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymemory/SensoryMemoryBackgroundTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymemory/SensoryMemoryImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymemory/SensoryMemoryListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymemory/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymotormemory/BasicSensoryMotorMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymotormemory/BasicSensoryMotorMemoryInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymotormemory/ProcessActionTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymotormemory/SensoryMotorMemory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymotormemory/SensoryMotorMemoryListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/sensorymotormemory/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/AttentionBackgroundTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/CueBackgroundTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/UpdateCsmBackgroundTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/Workspace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/WorkspaceContent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/WorkspaceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/WorkspaceListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/structurebuildingcodelets/BasicStructureBuildingCodelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/structurebuildingcodelets/StructureBuildingCodelet.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/structurebuildingcodelets/StructureBuildingCodeletImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/structurebuildingcodelets/StructureBuildingCodeletModule.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/structurebuildingcodelets/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/workspacebuffers/BroadcastQueue.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/workspacebuffers/BroadcastQueueImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/workspacebuffers/WMBufferImpl_mem.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/workspacebuffers/WSBufferImpl_graph.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/workspacebuffers/WorkspaceBuffer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/lida/workspace/workspacebuffers/package.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/linars/CompoundTerm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/linars/Concept.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/linars/Memory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/linars/ProcessGoal.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/linars/Term.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/edu/memphis/ccrg/linars/TermNodeImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/logging/ExperimentPhase.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/logging/MotivationFormatter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/logging/MotivationMeasure.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/logging/some.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/LockedValueTypes.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/Launcher.form" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/Launcher.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/Util.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Camera.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Crossing.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Encoders/InformPredictionNar.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Entities/Bike.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Entities/Car.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Entities/Entity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Entities/Pedestrian.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/NarListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/OperatorPanel.form" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/OperatorPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Street.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/TrafficLight.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/crossing/Viewport.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/idmapping/IdentityMappingGUI.form" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/idmapping/IdentityMappingGUI.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/nargui/NarGUI.form" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/nargui/NarGUI.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Encoders/EntityToNarsese.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Encoders/InformLocationNar.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Encoders/InformPredictionNar.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Encoders/InformQaNar.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Encoders/MapEvidence.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Entities/Bike.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Entities/Car.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Entities/Entity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Entities/Pedestrian.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/MultiNarSetup.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/NarListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/OperatorPanel.form" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/OperatorPanel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/Prediction.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/VisualReasonerHeadless.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/applications/streetscene/VisualReasonerWithGUI.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/control/DerivationContext.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/control/InferenceControl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/control/concept/ProcessAnticipation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/control/concept/ProcessJudgment.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/control/concept/ProcessQuestion.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/control/concept/ProcessTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/BudgetValue.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/Item.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/Sentence.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/Stamp.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/TLink.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/Task.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/TaskLink.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/TermLink.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/TruthValue.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/entity/package-info.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/BudgetFunctions.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/CompositionalRules.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/LocalRules.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/RuleTables.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/StructuralRules.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/SyllogisticRules.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/TemporalRules.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/TruthFunctions.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/UtilityFunctions.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/inference/package-info.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/Eventable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/InputFileConsumer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/Multistepable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/NarseseConsumer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/Pluggable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/Resettable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/SensoryChannelConsumer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/TaskConsumer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/Timable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/interfaces/pub/Reasoner.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/Channel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/ConfigReader.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/Narsese.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/Parser.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/Symbols.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/Texts.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/events/AnswerHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/events/EventEmitter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/events/EventHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/events/Events.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/events/OutputHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/events/TextOutputHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/io/package-info.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/AbstractTerm.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Conjunction.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/DifferenceExt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/DifferenceInt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Disjunction.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Equivalence.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Image.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/ImageExt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/ImageInt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Implication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Inheritance.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Instance.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/InstanceProperty.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/IntersectionExt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/IntersectionInt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Interval.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Negation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Product.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Property.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/SetExt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/SetInt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/SetTensional.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Similarity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Statement.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Tense.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Terms.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Variable.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/Variables.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/language/package-info.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/main/Debug.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/main/Nar.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/main/NarNode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/main/Parameters.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/main/Shell.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/main/overview.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/FunctionOperator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/ImaginationSpace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/NullOperator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/Operation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/Operator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Anticipate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Believe.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Consider.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Doubt.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Evaluate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Feel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/FeelBusy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/FeelSatisfied.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Hesitate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Name.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Register.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Remind.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Want.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/mental/Wonder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/Add.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/Add0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/Attent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/Count.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/CutStr.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/Learn.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/Reflect.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/Say.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/SearchMOM.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/SearchMOS.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/SearchSB.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/SearchSOM.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/SearchSOS.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/operator/misc/SystemNar.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/Plugin.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/mental/Abbreviation.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/mental/ComplexEmotions.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/mental/Counting.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/mental/Emotions.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/mental/InternalExperience.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/perception/NarseseChannel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/perception/SensoryChannel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/perception/VisionChannel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/plugin/perception/VisualSpace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/storage/Bag.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/storage/Bag1.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/storage/Buffer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/storage/Distributor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/storage/InternalExperienceBuffer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/storage/package-info.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/ListUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/io/ChangedTextInput.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/io/ExampleFileInput.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/io/KeyboardInputExample.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/test/ConceptMonitor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/test/OutputCondition.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/test/OutputContainsCondition.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/test/OutputEmptyCondition.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/test/OutputNotContainsCondition.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/java/org/opennars/util/test/TuneTuffy.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/FontAwesome.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/config/defaultConfig.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/config/mvpConfig.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/config/visbles.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/defaultimg/defObjImg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/defaultimg/emtyCellImg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/defaultimg/food.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/defaultimg/multipleObjects.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/file.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/and_switch_light.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/complex1.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/dont_switch.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/dont_switch2.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/ex4.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/freq_generator.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/house.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/key2.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/pizzamaschine.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/pizzeria.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/simple.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/switchX4.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/switch_door_switch_light.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/uncertain_event.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/grid2d/uncertain_state.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/language/language_knowledge.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/launcher/microworld.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/launcher/nlp.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/launcher/opennars_logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/launcher/pong.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/launcher/predict.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/launcher/rover.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/launcher/testchamber.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/collections-generic-4.01.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/colt-1.2.0.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/concurrent-1.3.4.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jcommon-1.0.16.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jfreechart-1.0.13.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-3d-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-3d-demos-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-algorithms-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-api-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-graph-impl-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-io-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-jai-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-jai-samples-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-samples-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/jung-visualization-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/stax-api-1.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/vecmath-1.3.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/lib/wstx-asl-3.2.6.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/log4j.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/mapping/Knowledgegraph.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/microworld/agent.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/microworld/ball.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/microworld/bar.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/microworld/fire.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/microworld/food.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/application/detective.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/application/detective2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/application/toothbrush.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/application/toothbrush2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/application/vision.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal1.multistep.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal3.subtermMapping1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal4.everyday_reasoning.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal4.recursion.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal4.recursion.small.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal4.recursion.small.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal6.mln1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal7.implSeqABCsimple.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal7.predEquiv.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nal7.seq3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_memorize_precondition.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_memorize_precondition2_parallel.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_memorize_precondition2_sequential.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_memorize_precondition_sequence.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_memorize_precondition_var1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_memorize_precondition_var2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_memorize_precondition_var3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_multistep_1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_multistep_2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_multistep_3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_seqABC.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_spatialSeq1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/nars_transitivity.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/multi_step/stresstest_bird1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/README.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/desire.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal1.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal2.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal3.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal4.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.20.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.21.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.22.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.23.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.24.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.25.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.26.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.27.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.28.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.29.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal5.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.20.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.21.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.22.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.23.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.24.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.25.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.26.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.27.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.birdClaimedByBob.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.can_of_worms.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.nlp1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.nlp2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.redundant.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.symmetry.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal6.uncle.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.35.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.36.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.concurrentEqual.res.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.concurrentImpl.abd.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.concurrentImpl.ded.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.concurrentImpl.goal.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.concurrentImpl.ind.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.conj.decompose.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.decomposeCompound.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.eventInduction1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.eventInduction2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.par.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.predictiveImpl.goal.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.retroImplSeq.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.retrospectiveImpl.goal.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.revrev.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.vardetach1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.vardetach2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.vardetach3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal7.vardetach4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.20.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.21.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.23.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.24.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.25.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.27.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.1.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.2.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.2.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.2.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.2.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.3.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.3.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.3.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.3.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.3.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.4.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.4.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.4.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.4.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.4.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.4.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.4.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.5.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.add.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.count.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8.reflect.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal8_list.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.anticipate1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.believe1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.believe2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.doubt.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.evaluate1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.evaluate2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.hesitate.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.want1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.want12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.want2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.wonder1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/nal9.wonder2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/single_step/notcontain.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/nal/stability/long_term_stability.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/rebel.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/blog/base.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/blog/index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/blog/m.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/element-style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/index0.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/jquery.jsonview.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/manager.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/css/x-index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/gzbg.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/line.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/logo/login_bg.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/logo/logo-0.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/logo/logo-3.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/logo/logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/logo/logo_o.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/me.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/images/tan_weixin_qr_1.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/index999.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/fly/fly.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/fly/layim.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/demo/1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/demo/2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/demo/3.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/demo/4.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/demo/5.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/demo/6.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/demo/7.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/desc.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/logo-2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/layui/logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/other/upyun.png-t=1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/images/sentsin/night.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/build/images/face/2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/build/images/face/38.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/release/laydate/dist/laydate.js-v=201711160800" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/contextMenu.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/global.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/independents.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/layui.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/layui.demo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/layui0.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/menu.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/modules/code.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/modules/layer/default/layer.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/css/modules/layim/layim.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/font/iconfont.eot-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/font/iconfont.svg-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/font/iconfont.ttf-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/font/iconfont.woff-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/images/face/2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/images/face/20.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/carousel.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/code.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/element.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/flow.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/form.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/getList1.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/jquery.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/laydate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/layedit.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/layer.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/layim-原版.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/layim.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/laypage.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/laytpl.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layer-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layim-mobile-open.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layim-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/upload-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/zepto.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/table.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/tree.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/upload.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/lay/modules/util.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/layui/src/layui.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/demo.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/face.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/global/global.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/jie.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/req.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/socket.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/socket000.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/strophe-1.2.8.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/strophe-1.2.8.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/user.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/webim.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/res.layui.com/mods/websdk.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/chatC.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/chat_win.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/chatlog.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/createGroup.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/createGroup0.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/demo/index.htm" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/demo/laydate.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/demo/layer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/demo/laypage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/demo/laytpl.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/find.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/getInformation.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/layim/json/getList1.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/layim/json/layim.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/chatC/www.layui.com/msgbox.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/d3.v4.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/d3.v4.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/directives.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/html2canvas.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/iconfont.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/jquery.jsonview.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/jquery.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/kgbuilder.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/lodash.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/sidebarAdmin.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/js/vue.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/react_build/layout.worker.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/codemirror-cypher.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/codemirror.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/cy2neod3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/cypher.datatable.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/data.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/jquery.dataTables.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/neo4d3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/neod3-visualization.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/neod3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/sweet-alert.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/scripts/vendor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/Animation.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/Common.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/IconStylesheet.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/NotoSans-Regular.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/NotoSansArabic-Regular.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/OrbitControls.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/Roboto-msdf.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/Roboto-msdf.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/Stats.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/TrackballControls.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/analytics.kineviz.countly.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/app.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/app.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/app.nodeModules.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/app.reactdatagrid.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/bootstrap.bundle.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/bootstrap.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/catchreport.kineviz.com.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/crossfilter.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/cypher-editor-support.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/d3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/dc.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/dc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/default.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/font-awesome.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/fontawesome-webfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/graphxr-icons.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/graphxr-schema(1).json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/graphxr-schema.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/gui.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/hopscotch.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/hopscotch.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/html2canvas.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/iconfont.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/iconfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/icons.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/icons.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/jquery-ui.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/jquery-ui.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/jquery.fullscreen-min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/jquery.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/jszip.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/loader.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/loading.mp4" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/mapbox-gl.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/mapbox-gl.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/mathbox.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/mathbox.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/md5.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/pin.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/socket.io.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/three.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/threex.keyboardstate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/vendor.bundle.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/static/visual-query-mapping.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/codemirror-neo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/codemirror.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/cy2neo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/datatable.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/fonts/FontAwesome.otf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/fonts/fontawesome-webfont.eot" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/fonts/fontawesome-webfont.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/fonts/fontawesome-webfont.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/fonts/fontawesome-webfont.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/gh-fork-ribbon.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/images/maze-black.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/neod3.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/sweet-alert.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/styles/vendor.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/wangeditor/fonts/w-e-icon.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/wangeditor/wangEditor.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/wangeditor/wangEditor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/wangeditor/wangEditor.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/wangeditor/wangEditor.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/static/wangeditor/wangEditor.min.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/kg/home.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/kg/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/kg/popse.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/share/focus.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/share/footer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/share/header.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/share/headerAdmin.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/share/layout.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/share/layout3.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/templates/share/layoutAdmin.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/main/resources/unittest/TestscriptRetbool.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/AddOp.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/AddOp0.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/TreeConverter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/csv2neo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/edu/memphis/ccrg/lida/episodicmemory/neo/Lida2narseseTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/edu/memphis/ccrg/lida/episodicmemory/neo/NeoNodeStructureTest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/edu/memphis/ccrg/lida/episodicmemory/neo/Nnode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/java/edu/memphis/ccrg/lida/episodicmemory/neo/TestGan.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/resources/OLPC_2_35.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/resources/OLPC_2_35.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/src/test/resources/logback.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/FontAwesome.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/config/defaultConfig.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/config/mvpConfig.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/config/visbles.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/defaultimg/defObjImg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/defaultimg/emtyCellImg.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/defaultimg/food.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/defaultimg/multipleObjects.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/file.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/and_switch_light.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/complex1.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/dont_switch.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/dont_switch2.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/ex4.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/freq_generator.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/house.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/key2.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/pizzamaschine.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/pizzeria.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/simple.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/switchX4.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/switch_door_switch_light.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/uncertain_event.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/grid2d/uncertain_state.lvl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/language/language_knowledge.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/launcher/microworld.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/launcher/nlp.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/launcher/opennars_logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/launcher/pong.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/launcher/predict.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/launcher/rover.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/launcher/testchamber.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/collections-generic-4.01.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/colt-1.2.0.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/concurrent-1.3.4.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jcommon-1.0.16.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jfreechart-1.0.13.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-3d-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-3d-demos-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-algorithms-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-api-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-graph-impl-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-io-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-jai-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-jai-samples-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-samples-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/jung-visualization-2.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/stax-api-1.0.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/vecmath-1.3.1.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/lib/wstx-asl-3.2.6.jar" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/log4j.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/mapping/Knowledgegraph.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/microworld/agent.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/microworld/ball.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/microworld/bar.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/microworld/fire.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/microworld/food.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/application/detective.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/application/detective2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/application/toothbrush.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/application/toothbrush2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/application/vision.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal1.multistep.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal3.subtermMapping1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal4.everyday_reasoning.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal4.recursion.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal4.recursion.small.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal4.recursion.small.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal6.mln1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal7.implSeqABCsimple.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal7.predEquiv.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nal7.seq3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_memorize_precondition.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_memorize_precondition2_parallel.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_memorize_precondition2_sequential.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_memorize_precondition_sequence.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_memorize_precondition_var1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_memorize_precondition_var2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_memorize_precondition_var3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_multistep_1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_multistep_2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_multistep_3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_seqABC.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_spatialSeq1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/nars_transitivity.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/multi_step/stresstest_bird1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/README.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/desire.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal1.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal2.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal3.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal4.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.20.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.21.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.22.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.23.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.24.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.25.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.26.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.27.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.28.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.29.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal5.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.20.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.21.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.22.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.23.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.24.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.25.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.26.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.27.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.birdClaimedByBob.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.can_of_worms.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.nlp1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.nlp2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.redundant.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.symmetry.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal6.uncle.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.15.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.35.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.36.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.concurrentEqual.res.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.concurrentImpl.abd.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.concurrentImpl.ded.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.concurrentImpl.goal.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.concurrentImpl.ind.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.conj.decompose.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.decomposeCompound.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.eventInduction1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.eventInduction2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.par.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.predictiveImpl.goal.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.retroImplSeq.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.retrospectiveImpl.goal.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.revrev.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.vardetach1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.vardetach2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.vardetach3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal7.vardetach4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.10.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.11.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.13.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.14.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.16.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.17.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.18.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.19.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.20.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.21.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.23.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.24.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.25.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.27.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.8.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.1.9.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.2.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.2.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.2.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.2.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.3.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.3.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.3.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.3.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.3.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.4.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.4.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.4.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.4.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.4.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.4.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.4.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.0.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.3.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.5.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.6.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.5.7.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.add.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.count.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8.reflect.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal8_list.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.4.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.anticipate1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.believe1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.believe2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.doubt.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.evaluate1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.evaluate2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.hesitate.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.want1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.want12.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.want2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.wonder1.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/nal9.wonder2.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/single_step/notcontain.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/nal/stability/long_term_stability.nal" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/rebel.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/blog/base.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/blog/index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/blog/m.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/element-style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/index0.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/jquery.jsonview.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/manager.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/css/x-index.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/gzbg.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/line.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/logo/login_bg.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/logo/logo-0.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/logo/logo-3.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/logo/logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/logo/logo_o.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/me.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/images/tan_weixin_qr_1.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/index999.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/fly/fly.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/fly/layim.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/demo/1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/demo/2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/demo/3.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/demo/4.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/demo/5.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/demo/6.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/demo/7.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/desc.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/logo-2.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/layui/logo.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/other/upyun.png-t=1.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/images/sentsin/night.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/build/images/face/2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/build/images/face/38.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/release/laydate/dist/laydate.js-v=201711160800" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/contextMenu.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/global.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/independents.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/layui.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/layui.demo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/layui0.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/menu.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/modules/code.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/modules/layer/default/layer.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/css/modules/layim/layim.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/font/iconfont.eot-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/font/iconfont.svg-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/font/iconfont.ttf-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/font/iconfont.woff-v=220" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/images/face/2.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/images/face/20.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/carousel.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/code.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/element.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/flow.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/form.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/getList1.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/jquery.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/laydate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/layedit.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/layer.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/layim-原版.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/layim.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/laypage.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/laytpl.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layer-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layim-mobile-open.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/layim-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/upload-mobile.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/mobile/zepto.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/table.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/tree.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/upload.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/lay/modules/util.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/layui/src/layui.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/demo.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/face.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/global/global.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/jie.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/req.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/socket.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/socket000.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/strophe-1.2.8.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/strophe-1.2.8.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/user.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/webim.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/res.layui.com/mods/websdk.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/chatC.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/chat_win.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/chatlog.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/createGroup.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/createGroup0.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/demo/index.htm" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/demo/laydate.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/demo/layer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/demo/laypage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/demo/laytpl.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/find.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/getInformation.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/layim/json/getList1.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/layim/json/layim.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/chatC/www.layui.com/msgbox.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/d3.v4.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/d3.v4.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/directives.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/html2canvas.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/iconfont.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/jquery.jsonview.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/jquery.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/kgbuilder.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/lodash.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/sidebarAdmin.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/js/vue.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/react_build/layout.worker.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/codemirror-cypher.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/codemirror.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/cy2neod3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/cypher.datatable.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/data.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/jquery.dataTables.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/neo4d3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/neod3-visualization.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/neod3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/sweet-alert.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/scripts/vendor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/Animation.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/Common.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/IconStylesheet.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/NotoSans-Regular.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/NotoSansArabic-Regular.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/OrbitControls.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/Roboto-msdf.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/Roboto-msdf.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/Stats.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/TrackballControls.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/analytics.kineviz.countly.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/app.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/app.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/app.nodeModules.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/app.reactdatagrid.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/bootstrap.bundle.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/bootstrap.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/catchreport.kineviz.com.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/contextMenu.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/crossfilter.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/cypher-editor-support.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/d3.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/dc.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/dc.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/default.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/font-awesome.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/fontawesome-webfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/graphxr-icons.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/graphxr-schema(1).json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/graphxr-schema.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/gui.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/hopscotch.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/hopscotch.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/html2canvas.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/iconfont.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/iconfont.woff2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/icons.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/icons.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/jquery-ui.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/jquery-ui.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/jquery.fullscreen-min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/jquery.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/jszip.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/loader.gif" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/loading.mp4" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/mapbox-gl.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/mapbox-gl.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/mathbox.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/mathbox.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/md5.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/pin.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/socket.io.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/style.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/three.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/threex.keyboardstate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/vendor.bundle.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/static/visual-query-mapping.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/codemirror-neo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/codemirror.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/cy2neo.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/datatable.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/fonts/FontAwesome.otf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/fonts/fontawesome-webfont.eot" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/fonts/fontawesome-webfont.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/fonts/fontawesome-webfont.ttf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/fonts/fontawesome-webfont.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/gh-fork-ribbon.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/images/maze-black.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/neod3.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/sweet-alert.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/styles/vendor.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/wangeditor/fonts/w-e-icon.woff" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/wangeditor/wangEditor.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/wangeditor/wangEditor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/wangeditor/wangEditor.min.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/wangeditor/wangEditor.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/static/wangeditor/wangEditor.min.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/kg/home.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/kg/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/kg/popse.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/share/focus.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/share/footer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/share/header.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/share/headerAdmin.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/share/layout.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/share/layout3.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/templates/share/layoutAdmin.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/classes/unittest/TestscriptRetbool.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/test-classes/OLPC_2_35.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/test-classes/OLPC_2_35.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/target/test-classes/logback.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/忽略的文件.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/linars_java/有lab+有xr+用于ai整合linars参考.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_process_gtree_task.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_stamp.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_symbols.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/LIDA_framework_translation_report.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/add_init_files.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/bag1_backup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/bag1_fixed.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/bag1_new.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/buffer_fixed.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/check_alifeagent.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/check_import_path.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/fix_agent_xml.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/fix_agent_xml_case.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/fix_directories.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/fix_imports.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/fix_imports_case.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/fix_imports_new.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/fix_paths.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/java_files.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/rename_directories.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/run_flask.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/run_web.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/setup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/temp_page.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/test_empty_domain.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/test_import.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/test_neo4j.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/test_variables.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/translated_files_lida.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/translated_files_nars.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/workspace (2).xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/一些杂项/workspace.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/NARS与LIDA记忆系统整合方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/NARS重构分析与优化建议.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/Palantir与本项目可执行图式比较分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/代码结构分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/全局广播与学习机制改进方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/动机管理系统分析与优化.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/可执行图式执行流程分析与优化.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/可执行图式操作模型优化建议.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/可执行图式核心架构设计与优化方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/可执行图式结构模板.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/图式模型设计与结构分析整合版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/图式激活扩散优化-第一部分.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/图式激活扩散优化-第三部分.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/图式激活扩散优化-第二部分.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/基于大模型的可执行图式自动构建方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/多位数加法可执行图式-代码实现建议.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/多位数加法可执行图式-优化图谱导入形式.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/多位数加法可执行图式-优化版本.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/多位数加法可执行图式-图谱导入形式.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/多位数加法可执行图式-当前实现版本.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/多位数加法可执行图式-点边结构表示.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/情绪情感模块设计.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/搜索机制作为可执行图式工具-深度分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/搜索机制作为可执行图式工具-理论基础.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/搜索机制作为可执行图式工具.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/注意力机制与全局工作空间理论.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/注意力机制改进方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/激活扩散机制-理论与实现.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/编程语言底层API与AGI类人能力实现分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/自然语言编译执行与NARS三段论推理集成综合文档.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/自然语言编译执行与图结构投票机制.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/自然语言编译执行理论-详细版.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/自然语言编译执行综合文档.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/行动选择与程序性记忆理论.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/非公理推理系统NARS理论.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/项目与开源认知架构比较分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/项目中文TODO注释整理.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/项目代码实现状态分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/项目分析进度与计划.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/项目模块深度分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/项目理论总纲.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文档java/项目理论架构概述与总优化方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/性能指标.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/架构分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/编译执行流程设计.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/自然语言处理理论.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/自然语言编译执行专业理论.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/认知图谱优化策略.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/认知图谱推理算法.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/认知神经科学基础.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/详细设计.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/语言模型技术分析.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/分析文档/高级推理模式.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/启动确认清单.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/开发计划/认知图谱优化.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/待命确认书.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/测试方案/单元测试用例.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/测试方案/集成测试实现.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/理论工作总结报告.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/编码实施方案.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/项目待命状态报告.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/项目总命令.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目构建/项目进度与计划.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager" show_ignored="true">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2wOKGQN3cJmCK9uHH8vtKBHLPt1" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.run0.executor": "Debug",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "codeReviewSummary": "[]",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "G:/linars/linars-all-by-ag",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "SDK",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.4213564",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="G:\linars\linars-all-by-ag\理论架构" />
      <recent name="G:\linars\linars-all-by-ag\linars\org\opennars\language" />
      <recent name="G:\linars\linars-all-by-ag\linars\edu\memphis\ccrg\lida\Alifeagent\configs" />
      <recent name="G:\linars\linars-all-by-ag\configs" />
      <recent name="G:\linars\linars-all-by-ag\linars\edu\memphis\ccrg\lida\Framework\Initialization" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\linars\linars-all-by-ag\linars\edu\memphis\ccrg\lida\Workspace\WorkspaceBuffers" />
      <recent name="G:\linars\linars-all-by-ag" />
      <recent name="G:\linars\linars-all-by-ag\linars" />
      <recent name="G:\linars\linars-all-by-ag\configs" />
      <recent name="G:\linars\linars-all-by-ag\linars\org\opennars\language" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="Babashka" factoryName="BabashkaLocalRepl" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="bbPath" value="" />
      <setting name="parameters" value="" />
      <option name="PARENT_ENVS" value="true" />
      <setting name="workingDir" value="" />
      <setting name="focusEditor" value="false" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Local" activateToolWindowBeforeRun="false">
      <method v="2" />
    </configuration>
    <configuration default="true" type="ClojureREPL" factoryName="Remote" activateToolWindowBeforeRun="false">
      <setting name="displayName" value="" />
      <setting name="host" value="" />
      <setting name="port" value="0" />
      <setting name="replType" value="SOCKET" />
      <setting name="configType" value="SPECIFY" />
      <setting name="replPortFileType" value="STANDARD" />
      <setting name="customPortFile" value="" />
      <setting name="fixLineNumbers" value="false" />
      <setting name="focusEditor" value="false" />
      <setting name="urlFile" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run0" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="linars-all-by-ag" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/alifeagent" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/alifeagent/run0.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.run0" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5364511b-dfb5-48bf-8534-d1f847bf722f" name="更改" comment="" />
      <created>1745907084316</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745907084316</updated>
      <workItem from="1745907085252" duration="27804000" />
      <workItem from="1745934985852" duration="14293000" />
      <workItem from="1745977632819" duration="1511000" />
      <workItem from="1745982907092" duration="4682000" />
      <workItem from="1745987639239" duration="1999000" />
      <workItem from="1745989652931" duration="3318000" />
      <workItem from="1745992997627" duration="16245000" />
      <workItem from="1746009288254" duration="782000" />
      <workItem from="1746010084044" duration="182000" />
      <workItem from="1746010286380" duration="6265000" />
      <workItem from="1746020416967" duration="2208000" />
      <workItem from="1746022640716" duration="2879000" />
      <workItem from="1746025752912" duration="12086000" />
      <workItem from="1746053277349" duration="3753000" />
      <workItem from="1746070634871" duration="59000" />
      <workItem from="1746070718811" duration="26000" />
      <workItem from="1746070760580" duration="377000" />
      <workItem from="1746071153174" duration="2694000" />
      <workItem from="1746073899726" duration="1841000" />
      <workItem from="1746075770030" duration="571000" />
      <workItem from="1746081507196" duration="4764000" />
      <workItem from="1746086303422" duration="4260000" />
      <workItem from="1746090605073" duration="5333000" />
      <workItem from="1746095963273" duration="1473000" />
      <workItem from="1746097452507" duration="2978000" />
      <workItem from="1746100450812" duration="5751000" />
      <workItem from="1746106237300" duration="2198000" />
      <workItem from="1746108452047" duration="5968000" />
      <workItem from="1746115403047" duration="4259000" />
      <workItem from="1746119680703" duration="1376000" />
      <workItem from="1746121075439" duration="1721000" />
      <workItem from="1746122813054" duration="2849000" />
      <workItem from="1746152465024" duration="3881000" />
      <workItem from="1746156605586" duration="1715000" />
      <workItem from="1746158334112" duration="9812000" />
      <workItem from="1746169566553" duration="5522000" />
      <workItem from="1746175103651" duration="8793000" />
      <workItem from="1746183917199" duration="2445000" />
      <workItem from="1746186378981" duration="3589000" />
      <workItem from="1746190007530" duration="3415000" />
      <workItem from="1746193633361" duration="1445000" />
      <workItem from="1746195097978" duration="4757000" />
      <workItem from="1746199876641" duration="1092000" />
      <workItem from="1746200983246" duration="211000" />
      <workItem from="1746201209162" duration="10814000" />
      <workItem from="1746237590542" duration="20529000" />
      <workItem from="1746259425661" duration="1329000" />
      <workItem from="1746260770431" duration="9518000" />
      <workItem from="1746272229405" duration="24834000" />
      <workItem from="1746326254070" duration="11401000" />
      <workItem from="1746338338325" duration="16800000" />
      <workItem from="1746355176058" duration="9879000" />
      <workItem from="1746365080091" duration="24053000" />
      <workItem from="1746412974682" duration="17961000" />
      <workItem from="1746810460696" duration="65000" />
      <workItem from="1757007649182" duration="1495000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="大改-首次提交" />
    <option name="LAST_COMMIT_MESSAGE" value="大改-首次提交" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_starter.py</url>
          <line>51</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/SelectConceptTask.py</url>
          <line>37</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>33</line>
          <option name="timeStamp" value="45" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>183</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/operator/mental/doubt.py</url>
          <line>33</line>
          <option name="timeStamp" value="47" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/concept.py</url>
          <line>387</line>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/operator/mental/consider.py</url>
          <line>36</line>
          <option name="timeStamp" value="51" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/operator/mental/remind.py</url>
          <line>35</line>
          <option name="timeStamp" value="52" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/buffer.py</url>
          <line>208</line>
          <option name="timeStamp" value="53" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>608</line>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>403</line>
          <option name="timeStamp" value="56" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>234</line>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>157</line>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>79</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>181</line>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>26</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/buffer.py</url>
          <line>100</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>409</line>
          <option name="timeStamp" value="66" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>248</line>
          <option name="timeStamp" value="71" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>265</line>
          <option name="timeStamp" value="73" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>262</line>
          <option name="timeStamp" value="74" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>259</line>
          <option name="timeStamp" value="75" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>251</line>
          <option name="timeStamp" value="77" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/statement.py</url>
          <line>79</line>
          <option name="timeStamp" value="78" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>335</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/entity/sentence.py</url>
          <line>85</line>
          <option name="timeStamp" value="80" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>215</line>
          <option name="timeStamp" value="81" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/FrameworkModuleImpl.py</url>
          <line>123</line>
          <option name="timeStamp" value="82" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WSBufferImpl_graph.py</url>
          <line>40</line>
          <option name="timeStamp" value="83" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>213</line>
          <option name="timeStamp" value="84" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>33</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SubGraphSet.py</url>
          <line>30</line>
          <option name="timeStamp" value="87" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>61</line>
          <option name="timeStamp" value="88" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>537</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>128</line>
          <option name="timeStamp" value="90" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_anticipation.py</url>
          <line>223</line>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/compositional_rules.py</url>
          <line>253</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/rule_tables.py</url>
          <line>69</line>
          <option name="timeStamp" value="93" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/buffer.py</url>
          <line>166</line>
          <option name="timeStamp" value="94" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>228</line>
          <option name="timeStamp" value="96" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WMBufferImpl_mem.py</url>
          <line>36</line>
          <option name="timeStamp" value="97" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WMBufferImpl_mem.py</url>
          <line>35</line>
          <option name="timeStamp" value="98" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_xml_factory.py</url>
          <line>1457</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_xml_factory.py</url>
          <line>1043</line>
          <option name="timeStamp" value="100" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_xml_factory.py</url>
          <line>1028</line>
          <option name="timeStamp" value="102" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_starter.py</url>
          <line>44</line>
          <option name="timeStamp" value="103" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Initialization/agent_starter.py</url>
          <line>42</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>23</line>
          <option name="timeStamp" value="107" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>198</line>
          <option name="timeStamp" value="112" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>64</line>
          <option name="timeStamp" value="113" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/main/nar.py</url>
          <line>111</line>
          <option name="timeStamp" value="114" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceBuffers/WMBufferImpl_mem.py</url>
          <line>37</line>
          <option name="timeStamp" value="115" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>532</line>
          <option name="timeStamp" value="116" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>91</line>
          <option name="timeStamp" value="117" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>30</line>
          <option name="timeStamp" value="120" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>209</line>
          <option name="timeStamp" value="121" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>86</line>
          <option name="timeStamp" value="122" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Tasks/FrameworkTaskImpl.py</url>
          <line>150</line>
          <option name="timeStamp" value="123" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>47</line>
          <option name="timeStamp" value="124" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>101</line>
          <option name="timeStamp" value="129" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>45</line>
          <option name="timeStamp" value="131" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>205</line>
          <option name="timeStamp" value="132" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>824</line>
          <option name="timeStamp" value="148" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>589</line>
          <option name="timeStamp" value="149" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>827</line>
          <option name="timeStamp" value="150" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkImpl.py</url>
          <line>147</line>
          <option name="timeStamp" value="151" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/LinkImpl.py</url>
          <line>174</line>
          <option name="timeStamp" value="152" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>451</line>
          <option name="timeStamp" value="153" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>137</line>
          <option name="timeStamp" value="154" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>73</line>
          <option name="timeStamp" value="155" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Framework/Shared/NodeStructureImpl.py</url>
          <line>75</line>
          <option name="timeStamp" value="161" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>180</line>
          <option name="timeStamp" value="162" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Alifeagent/Featuredetectors/listen_detector.py</url>
          <line>183</line>
          <option name="timeStamp" value="163" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamNodeImpl.py</url>
          <line>30</line>
          <option name="timeStamp" value="165" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>168</line>
          <option name="timeStamp" value="166" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Data/neo_util.py</url>
          <line>155</line>
          <option name="timeStamp" value="167" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/bag1.py</url>
          <line>172</line>
          <option name="timeStamp" value="169" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/bag1.py</url>
          <line>74</line>
          <option name="timeStamp" value="170" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/temporal_rules.py</url>
          <line>58</line>
          <option name="timeStamp" value="173" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/entity/stamp.py</url>
          <line>424</line>
          <option name="timeStamp" value="174" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>154</line>
          <option name="timeStamp" value="177" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/inference_control.py</url>
          <line>75</line>
          <option name="timeStamp" value="179" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>63</line>
          <option name="timeStamp" value="182" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Nlanguage/SubGraphSet.py</url>
          <line>45</line>
          <option name="timeStamp" value="185" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_task.py</url>
          <line>92</line>
          <option name="timeStamp" value="187" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>215</line>
          <option name="timeStamp" value="198" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>235</line>
          <option name="timeStamp" value="200" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>300</line>
          <option name="timeStamp" value="202" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>315</line>
          <option name="timeStamp" value="203" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>134</line>
          <option name="timeStamp" value="211" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>341</line>
          <option name="timeStamp" value="212" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>235</line>
          <option name="timeStamp" value="213" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/entity/stamp.py</url>
          <line>572</line>
          <option name="timeStamp" value="214" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>346</line>
          <option name="timeStamp" value="224" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>192</line>
          <option name="timeStamp" value="225" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>151</line>
          <option name="timeStamp" value="226" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>106</line>
          <option name="timeStamp" value="230" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/concept.py</url>
          <line>290</line>
          <option name="timeStamp" value="231" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>380</line>
          <option name="timeStamp" value="234" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>93</line>
          <option name="timeStamp" value="237" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/storage/bag1.py</url>
          <line>102</line>
          <option name="timeStamp" value="239" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>103</line>
          <option name="timeStamp" value="241" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>385</line>
          <option name="timeStamp" value="242" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>349</line>
          <option name="timeStamp" value="243" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>105</line>
          <option name="timeStamp" value="247" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>152</line>
          <option name="timeStamp" value="251" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>328</line>
          <option name="timeStamp" value="253" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1094</line>
          <option name="timeStamp" value="254" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>190</line>
          <option name="timeStamp" value="255" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/BasicPamInitializer.py</url>
          <line>45</line>
          <option name="timeStamp" value="263" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>417</line>
          <option name="timeStamp" value="286" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>936</line>
          <option name="timeStamp" value="289" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>487</line>
          <option name="timeStamp" value="291" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>525</line>
          <option name="timeStamp" value="293" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>515</line>
          <option name="timeStamp" value="294" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>133</line>
          <option name="timeStamp" value="297" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>930</line>
          <option name="timeStamp" value="299" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>939</line>
          <option name="timeStamp" value="302" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>562</line>
          <option name="timeStamp" value="303" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>162</line>
          <option name="timeStamp" value="304" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>215</line>
          <option name="timeStamp" value="305" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>252</line>
          <option name="timeStamp" value="309" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>257</line>
          <option name="timeStamp" value="310" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/concept/process_judgment.py</url>
          <line>220</line>
          <option name="timeStamp" value="313" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>205</line>
          <option name="timeStamp" value="314" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>226</line>
          <option name="timeStamp" value="315" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>291</line>
          <option name="timeStamp" value="316" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/ProcessGBufferTask.py</url>
          <line>126</line>
          <option name="timeStamp" value="319" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>232</line>
          <option name="timeStamp" value="321" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/control/derivation_context.py</url>
          <line>162</line>
          <option name="timeStamp" value="325" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>961</line>
          <option name="timeStamp" value="326" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>182</line>
          <option name="timeStamp" value="329" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/term.py</url>
          <line>515</line>
          <option name="timeStamp" value="330" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/variable.py</url>
          <line>139</line>
          <option name="timeStamp" value="331" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>565</line>
          <option name="timeStamp" value="333" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>326</line>
          <option name="timeStamp" value="336" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>437</line>
          <option name="timeStamp" value="338" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/implication.py</url>
          <line>203</line>
          <option name="timeStamp" value="343" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/statement.py</url>
          <line>343</line>
          <option name="timeStamp" value="344" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>430</line>
          <option name="timeStamp" value="345" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>399</line>
          <option name="timeStamp" value="349" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1206</line>
          <option name="timeStamp" value="353" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1014</line>
          <option name="timeStamp" value="354" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/Workspace/WorkspaceImpl.py</url>
          <line>255</line>
          <option name="timeStamp" value="358" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/statement.py</url>
          <line>237</line>
          <option name="timeStamp" value="361" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>284</line>
          <option name="timeStamp" value="362" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>290</line>
          <option name="timeStamp" value="363" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>299</line>
          <option name="timeStamp" value="364" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>307</line>
          <option name="timeStamp" value="365" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>428</line>
          <option name="timeStamp" value="366" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/language/implication.py</url>
          <line>174</line>
          <option name="timeStamp" value="367" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>198</line>
          <option name="timeStamp" value="372" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>193</line>
          <option name="timeStamp" value="375" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/memory.py</url>
          <line>207</line>
          <option name="timeStamp" value="376" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>193</line>
          <option name="timeStamp" value="377" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/Tasks/GoalBackgroundTask.py</url>
          <line>307</line>
          <option name="timeStamp" value="381" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>369</line>
          <option name="timeStamp" value="383" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>340</line>
          <option name="timeStamp" value="385" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>476</line>
          <option name="timeStamp" value="386" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>472</line>
          <option name="timeStamp" value="387" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>617</line>
          <option name="timeStamp" value="388" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>643</line>
          <option name="timeStamp" value="395" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>168</line>
          <option name="timeStamp" value="396" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>712</line>
          <option name="timeStamp" value="397" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>703</line>
          <option name="timeStamp" value="398" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>721</line>
          <option name="timeStamp" value="399" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>743</line>
          <option name="timeStamp" value="400" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1023</line>
          <option name="timeStamp" value="401" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>776</line>
          <option name="timeStamp" value="402" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>627</line>
          <option name="timeStamp" value="405" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1358</line>
          <option name="timeStamp" value="406" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>286</line>
          <option name="timeStamp" value="407" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/io/narsese.py</url>
          <line>71</line>
          <option name="timeStamp" value="408" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>805</line>
          <option name="timeStamp" value="409" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/process_goal.py</url>
          <line>1755</line>
          <option name="timeStamp" value="411" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>817</line>
          <option name="timeStamp" value="412" />
        </line-breakpoint>
        <line-breakpoint suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/linars/compound_term.py</url>
          <line>252</line>
          <option name="timeStamp" value="413" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/PAM/PamImpl0.py</url>
          <line>498</line>
          <option name="timeStamp" value="414" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/local_rules_helper.py</url>
          <line>123</line>
          <option name="timeStamp" value="419" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/linars/org/opennars/inference/local_rules_helper.py</url>
          <line>130</line>
          <option name="timeStamp" value="420" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/linars_all_by_ag$run0.coverage" NAME="run0 覆盖结果" MODIFIED="1746437106026" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/linars/edu/memphis/ccrg/lida/alifeagent" />
  </component>
</project>