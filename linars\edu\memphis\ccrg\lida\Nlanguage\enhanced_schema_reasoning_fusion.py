"""
增强版图式搜索与推理融合系统
将图式搜索机制与三段论推理深度融合，实现搜索驱动的推理和推理引导的搜索

主要特性：
1. 图式搜索与三段论推理的深度融合
2. 搜索驱动的推理触发机制
3. 推理引导的搜索优化
4. 动态搜索策略调整
5. 上下文感知的图式匹配
6. 推理结果的图式化表示
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import threading
import numpy as np
from abc import ABC, abstractmethod

# NARS推理相关导入
try:
    from linars.org.opennars.inference.enhanced_reasoning_engine import (
        get_enhanced_reasoning_engine, ReasoningRequest, ReasoningStrategy, ReasoningResult
    )
    from linars.org.opennars.inference.syllogistic_rules import SyllogisticRules
    from linars.org.opennars.entity.task import Task
    from linars.org.opennars.entity.sentence import Sentence
    from linars.org.opennars.entity.truth_value import TruthValue
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

# LIDA图式相关导入
try:
    from linars.edu.memphis.ccrg.lida.Nlanguage.NarsSchemaIntegration import NarsSchemaIntegration
    from linars.edu.memphis.ccrg.lida.Nlanguage.SchemaSearchEngine import SchemaSearchEngine
    from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
    from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
    LIDA_AVAILABLE = True
except ImportError:
    LIDA_AVAILABLE = False


class SearchReasoningMode(Enum):
    """搜索推理模式"""
    SEARCH_DRIVEN = "search_driven"      # 搜索驱动推理
    REASONING_GUIDED = "reasoning_guided"  # 推理引导搜索
    INTEGRATED = "integrated"            # 深度集成模式
    ADAPTIVE = "adaptive"               # 自适应模式


class FusionStrategy(Enum):
    """融合策略"""
    SEQUENTIAL = "sequential"    # 顺序执行
    PARALLEL = "parallel"       # 并行执行
    INTERLEAVED = "interleaved" # 交替执行
    DYNAMIC = "dynamic"         # 动态调整


@dataclass
class SchemaReasoningContext:
    """图式推理上下文"""
    context_id: str
    search_query: str
    reasoning_premises: List[Any]
    matched_schemas: List[Dict] = field(default_factory=list)
    reasoning_results: List[Any] = field(default_factory=list)
    fusion_mode: SearchReasoningMode = SearchReasoningMode.INTEGRATED
    confidence: float = 0.0
    created_time: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    
    def update_confidence(self, new_confidence: float):
        """更新置信度"""
        self.confidence = (self.confidence + new_confidence) / 2
        self.last_updated = time.time()


@dataclass
class FusionResult:
    """融合结果"""
    result_id: str
    search_results: List[Dict]
    reasoning_conclusions: List[Any]
    integrated_knowledge: List[Dict]
    confidence: float
    execution_time: float
    fusion_strategy_used: FusionStrategy
    success: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


class SchemaReasoningFusionEngine:
    """图式推理融合引擎"""
    
    def __init__(self, schema_search_engine=None, reasoning_engine=None):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 核心组件
        self.schema_search_engine = schema_search_engine
        self.reasoning_engine = reasoning_engine or (get_enhanced_reasoning_engine() if NARS_AVAILABLE else None)
        
        # 融合上下文管理
        self.fusion_contexts: Dict[str, SchemaReasoningContext] = {}
        self.active_fusions: Dict[str, asyncio.Task] = {}
        
        # 融合策略配置
        self.fusion_strategies = {
            FusionStrategy.SEQUENTIAL: self._sequential_fusion,
            FusionStrategy.PARALLEL: self._parallel_fusion,
            FusionStrategy.INTERLEAVED: self._interleaved_fusion,
            FusionStrategy.DYNAMIC: self._dynamic_fusion
        }
        
        # 配置参数
        self.config = {
            'default_fusion_strategy': FusionStrategy.DYNAMIC,
            'max_concurrent_fusions': 10,
            'search_timeout': 10.0,
            'reasoning_timeout': 15.0,
            'confidence_threshold': 0.3,
            'max_schema_matches': 20,
            'integration_depth': 3
        }
        
        # 性能统计
        self.performance_stats = {
            'total_fusions': 0,
            'successful_fusions': 0,
            'search_operations': 0,
            'reasoning_operations': 0,
            'integration_operations': 0,
            'average_fusion_time': 0.0,
            'strategy_performance': defaultdict(lambda: {'count': 0, 'success': 0, 'avg_time': 0.0})
        }
        
        # 线程控制
        self.lock = threading.RLock()
        self.running = True
        
        # 启动融合监控
        self._start_fusion_monitor()
    
    def _start_fusion_monitor(self):
        """启动融合监控"""
        def monitor_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self._fusion_monitor())
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    async def _fusion_monitor(self):
        """融合监控循环"""
        while self.running:
            try:
                # 清理完成的融合任务
                await self._cleanup_completed_fusions()
                
                # 监控融合性能
                await self._monitor_fusion_performance()
                
                await asyncio.sleep(1.0)
                
            except Exception as e:
                self.logger.error(f"Error in fusion monitor: {e}")
                await asyncio.sleep(5.0)
    
    async def create_fusion_context(self, search_query: str, reasoning_premises: List[Any],
                                  mode: SearchReasoningMode = None) -> str:
        """创建融合上下文"""
        if mode is None:
            mode = SearchReasoningMode.INTEGRATED
        
        context_id = f"fusion_{uuid.uuid4().hex[:8]}"
        
        context = SchemaReasoningContext(
            context_id=context_id,
            search_query=search_query,
            reasoning_premises=reasoning_premises,
            fusion_mode=mode
        )
        
        with self.lock:
            self.fusion_contexts[context_id] = context
        
        self.logger.info(f"Created fusion context: {context_id}")
        return context_id
    
    async def execute_fusion(self, context_id: str, 
                           strategy: FusionStrategy = None) -> Optional[FusionResult]:
        """执行图式推理融合"""
        if strategy is None:
            strategy = self.config['default_fusion_strategy']
        
        with self.lock:
            if context_id not in self.fusion_contexts:
                self.logger.error(f"Fusion context not found: {context_id}")
                return None
            
            if len(self.active_fusions) >= self.config['max_concurrent_fusions']:
                self.logger.warning("Maximum concurrent fusions reached")
                return None
            
            context = self.fusion_contexts[context_id]
        
        # 创建融合任务
        fusion_task = asyncio.create_task(
            self._execute_fusion_strategy(context, strategy)
        )
        
        with self.lock:
            self.active_fusions[context_id] = fusion_task
        
        try:
            result = await fusion_task
            self._update_performance_stats(strategy, result)
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing fusion for context {context_id}: {e}")
            return None
        
        finally:
            with self.lock:
                if context_id in self.active_fusions:
                    del self.active_fusions[context_id]
    
    async def _execute_fusion_strategy(self, context: SchemaReasoningContext, 
                                     strategy: FusionStrategy) -> FusionResult:
        """执行融合策略"""
        start_time = time.time()
        
        try:
            # 选择并执行融合策略
            fusion_func = self.fusion_strategies.get(strategy, self._dynamic_fusion)
            result = await fusion_func(context)
            
            execution_time = time.time() - start_time
            
            return FusionResult(
                result_id=f"result_{context.context_id}",
                search_results=result.get('search_results', []),
                reasoning_conclusions=result.get('reasoning_conclusions', []),
                integrated_knowledge=result.get('integrated_knowledge', []),
                confidence=result.get('confidence', 0.0),
                execution_time=execution_time,
                fusion_strategy_used=strategy,
                success=result.get('success', False),
                metadata=result.get('metadata', {})
            )
            
        except Exception as e:
            self.logger.error(f"Error in fusion strategy execution: {e}")
            return FusionResult(
                result_id=f"result_{context.context_id}",
                search_results=[],
                reasoning_conclusions=[],
                integrated_knowledge=[],
                confidence=0.0,
                execution_time=time.time() - start_time,
                fusion_strategy_used=strategy,
                success=False,
                metadata={'error': str(e)}
            )
    
    async def _sequential_fusion(self, context: SchemaReasoningContext) -> Dict[str, Any]:
        """顺序融合策略"""
        # 1. 首先执行图式搜索
        search_results = await self._perform_schema_search(context)
        
        # 2. 基于搜索结果执行推理
        reasoning_results = await self._perform_reasoning_with_schemas(context, search_results)
        
        # 3. 集成结果
        integrated_knowledge = await self._integrate_search_reasoning_results(
            search_results, reasoning_results)
        
        return {
            'search_results': search_results,
            'reasoning_conclusions': reasoning_results,
            'integrated_knowledge': integrated_knowledge,
            'confidence': context.confidence,
            'success': len(integrated_knowledge) > 0
        }
    
    async def _parallel_fusion(self, context: SchemaReasoningContext) -> Dict[str, Any]:
        """并行融合策略"""
        # 同时执行搜索和推理
        search_task = asyncio.create_task(self._perform_schema_search(context))
        reasoning_task = asyncio.create_task(self._perform_basic_reasoning(context))
        
        search_results, reasoning_results = await asyncio.gather(
            search_task, reasoning_task, return_exceptions=True
        )
        
        # 处理异常
        if isinstance(search_results, Exception):
            search_results = []
        if isinstance(reasoning_results, Exception):
            reasoning_results = []
        
        # 集成结果
        integrated_knowledge = await self._integrate_search_reasoning_results(
            search_results, reasoning_results)
        
        return {
            'search_results': search_results,
            'reasoning_conclusions': reasoning_results,
            'integrated_knowledge': integrated_knowledge,
            'confidence': context.confidence,
            'success': len(integrated_knowledge) > 0
        }
    
    async def _interleaved_fusion(self, context: SchemaReasoningContext) -> Dict[str, Any]:
        """交替融合策略"""
        search_results = []
        reasoning_results = []
        integrated_knowledge = []
        
        # 交替执行搜索和推理
        for i in range(self.config['integration_depth']):
            # 执行一轮搜索
            batch_search = await self._perform_schema_search_batch(context, i)
            search_results.extend(batch_search)
            
            # 基于搜索结果执行推理
            batch_reasoning = await self._perform_reasoning_with_schemas(context, batch_search)
            reasoning_results.extend(batch_reasoning)
            
            # 部分集成
            batch_integration = await self._integrate_search_reasoning_results(
                batch_search, batch_reasoning)
            integrated_knowledge.extend(batch_integration)
            
            # 更新上下文
            context.matched_schemas.extend(batch_search)
            context.reasoning_results.extend(batch_reasoning)
        
        return {
            'search_results': search_results,
            'reasoning_conclusions': reasoning_results,
            'integrated_knowledge': integrated_knowledge,
            'confidence': context.confidence,
            'success': len(integrated_knowledge) > 0
        }
    
    async def _dynamic_fusion(self, context: SchemaReasoningContext) -> Dict[str, Any]:
        """动态融合策略"""
        # 根据上下文动态选择最优策略
        if len(context.reasoning_premises) > 5:
            # 复杂推理场景，使用顺序策略
            return await self._sequential_fusion(context)
        elif len(context.search_query.split()) < 3:
            # 简单搜索场景，使用并行策略
            return await self._parallel_fusion(context)
        else:
            # 中等复杂度，使用交替策略
            return await self._interleaved_fusion(context)
    
    async def _perform_schema_search(self, context: SchemaReasoningContext) -> List[Dict]:
        """执行图式搜索"""
        if not self.schema_search_engine:
            return []
        
        try:
            # 执行搜索
            search_results = await asyncio.wait_for(
                self._search_schemas(context.search_query),
                timeout=self.config['search_timeout']
            )
            
            # 更新统计
            self.performance_stats['search_operations'] += 1
            
            return search_results[:self.config['max_schema_matches']]
            
        except asyncio.TimeoutError:
            self.logger.warning(f"Schema search timeout for context {context.context_id}")
            return []
        except Exception as e:
            self.logger.error(f"Error in schema search: {e}")
            return []
    
    async def _perform_basic_reasoning(self, context: SchemaReasoningContext) -> List[Any]:
        """执行基础推理"""
        if not self.reasoning_engine or not context.reasoning_premises:
            return []
        
        try:
            # 创建推理请求
            request = ReasoningRequest(
                request_id=f"reasoning_{context.context_id}",
                premises=context.reasoning_premises,
                strategy=ReasoningStrategy.ADAPTIVE,
                timeout=self.config['reasoning_timeout']
            )
            
            # 提交推理请求
            request_id = await self.reasoning_engine.submit_reasoning_request(request)
            
            # 获取结果
            result = await self.reasoning_engine.get_reasoning_result(
                request_id, timeout=self.config['reasoning_timeout']
            )
            
            # 更新统计
            self.performance_stats['reasoning_operations'] += 1
            
            return result.conclusions if result and result.success else []
            
        except Exception as e:
            self.logger.error(f"Error in basic reasoning: {e}")
            return []
    
    async def _perform_reasoning_with_schemas(self, context: SchemaReasoningContext, 
                                           schemas: List[Dict]) -> List[Any]:
        """基于图式执行推理"""
        if not schemas:
            return await self._perform_basic_reasoning(context)
        
        # 将图式信息集成到推理前提中
        enhanced_premises = context.reasoning_premises.copy()
        
        for schema in schemas:
            # 将图式转换为推理前提
            schema_premise = await self._convert_schema_to_premise(schema)
            if schema_premise:
                enhanced_premises.append(schema_premise)
        
        # 执行增强推理
        try:
            request = ReasoningRequest(
                request_id=f"schema_reasoning_{context.context_id}",
                premises=enhanced_premises,
                strategy=ReasoningStrategy.ADAPTIVE,
                timeout=self.config['reasoning_timeout']
            )
            
            request_id = await self.reasoning_engine.submit_reasoning_request(request)
            result = await self.reasoning_engine.get_reasoning_result(
                request_id, timeout=self.config['reasoning_timeout']
            )
            
            return result.conclusions if result and result.success else []
            
        except Exception as e:
            self.logger.error(f"Error in schema-based reasoning: {e}")
            return []
    
    async def _integrate_search_reasoning_results(self, search_results: List[Dict], 
                                                reasoning_results: List[Any]) -> List[Dict]:
        """集成搜索和推理结果"""
        integrated_knowledge = []
        
        try:
            # 简化的集成逻辑
            for search_result in search_results:
                for reasoning_result in reasoning_results:
                    # 计算相关性
                    relevance = await self._calculate_relevance(search_result, reasoning_result)
                    
                    if relevance > self.config['confidence_threshold']:
                        integrated_item = {
                            'id': f"integrated_{uuid.uuid4().hex[:8]}",
                            'search_component': search_result,
                            'reasoning_component': reasoning_result,
                            'relevance': relevance,
                            'integration_type': 'search_reasoning_fusion',
                            'timestamp': time.time()
                        }
                        integrated_knowledge.append(integrated_item)
            
            # 更新统计
            self.performance_stats['integration_operations'] += 1
            
        except Exception as e:
            self.logger.error(f"Error integrating results: {e}")
        
        return integrated_knowledge
    
    async def _calculate_relevance(self, search_result: Dict, reasoning_result: Any) -> float:
        """计算搜索结果和推理结果的相关性"""
        # 简化的相关性计算
        try:
            # 基于关键词匹配计算相关性
            search_keywords = set(search_result.get('keywords', []))
            reasoning_keywords = set()  # 从推理结果中提取关键词
            
            if hasattr(reasoning_result, 'sentence') and hasattr(reasoning_result.sentence, 'term'):
                # 从NARS术语中提取关键词
                term_str = str(reasoning_result.sentence.term)
                reasoning_keywords = set(term_str.lower().split())
            
            if not search_keywords or not reasoning_keywords:
                return 0.5  # 默认中等相关性
            
            # 计算Jaccard相似度
            intersection = len(search_keywords & reasoning_keywords)
            union = len(search_keywords | reasoning_keywords)
            
            return intersection / union if union > 0 else 0.0
            
        except Exception as e:
            self.logger.warning(f"Error calculating relevance: {e}")
            return 0.3  # 默认低相关性
    
    def get_fusion_statistics(self) -> Dict[str, Any]:
        """获取融合统计信息"""
        with self.lock:
            return {
                'performance_stats': self.performance_stats.copy(),
                'active_contexts': len(self.fusion_contexts),
                'active_fusions': len(self.active_fusions),
                'config': self.config.copy()
            }
    
    def shutdown(self):
        """关闭融合引擎"""
        self.running = False
        self.logger.info("Schema reasoning fusion engine shutdown")


# 全局融合引擎实例
schema_reasoning_fusion_engine = None

def get_schema_reasoning_fusion_engine(schema_search_engine=None, reasoning_engine=None) -> SchemaReasoningFusionEngine:
    """获取图式推理融合引擎实例"""
    global schema_reasoning_fusion_engine
    if schema_reasoning_fusion_engine is None:
        schema_reasoning_fusion_engine = SchemaReasoningFusionEngine(schema_search_engine, reasoning_engine)
    return schema_reasoning_fusion_engine
